{"name": "ydp2ydap", "version": "0.0.1", "description": "ydp文件转换ydap服务", "type": "module", "main": "index.js", "scripts": {"start": "cross-env NODE_ENV=development node src ./demo/ydp/ac130112.ydp", "build": "pkg src/index.js -t node18-win-x86,node18-linux-x64,node18-macos-x64 -o build/ydp-helper", "postbuild": "node src/post-build"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "1.13.2", "chalk": "5.4.1", "dayjs": "1.11.13", "dotenv": "17.0.0", "fast-xml-parser": "5.2.5", "iconv-lite": "0.6.3", "lodash": "4.17.21", "openai": "5.8.2", "xlsx-template": "1.4.4"}, "devDependencies": {"cross-env": "7.0.3"}, "engines": {"node": ">=20.0.0"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}
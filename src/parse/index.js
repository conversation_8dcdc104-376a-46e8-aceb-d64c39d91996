'use strict';
import generatePage from './page.mjs';
import generateSections from './sections/index.mjs';
import { mergeAndConcatArrays } from './utils.js';
import convertDataSource from './data-source.js';
import convertIframes from './iframes.js';
import ScriptParser from './script/index.js';

/**
 * 解析 xml 元素内容
 * @param {Object} ydpJson xml文件转换生成的json内容
 * @param {string} pageName ydp文件名称
 */
async function parse(ydpJson, pageName) {
  // 生成动作
  const actions = mergeAndConcatArrays(
    await convertDataSource(ydpJson, pageName),
    await convertIframes(ydpJson, pageName),
  );
  console.log('actions:', actions);
  // 解析script标签内容
  const scriptParser = new ScriptParser();
  const scriptResult = await scriptParser.parse(ydpJson);
  // 生成页面结构
  const ydap = generatePage(ydpJson, scriptResult);
  // 生成sections章节
  await generateSections(ydpJson, pageName, ydap);

  return ydap;
}

export default parse;

import MCPClient from '../../llm/mcp/client/index.mjs';
import expressionPrompt from '../../llm/prompts/expression.mjs';

/**
 * 转换固定值
 */
const staticMap = {
  'rw=="w"': "%c.rw=='w'",
  'rw=="r"': "%c.rw=='r'",
};

/**
 * 获取固定值
 * @param {string} expression 表达式
 * @returns {string} 固定值
 */
const getStaticValue = (expression) => {
  const key = expression.replace('\s+', '').replace(/'|&quot;/g, '"');
  return staticMap[key];
};

/**
 * 转换表达式
 * @param {string} expression 表达式
 * @returns {string} 转换后的表达式
 */
const convertExpression = async (expression) => {
  // 查看是否有固定的表达式
  const staticValue = getStaticValue(expression);
  if (staticValue) return staticValue;

  const llmClient = new MCPClient(expressionPrompt);
  await llmClient.connect();
  const result = await llmClient.sendMessage(
    `请根据背景知识，将以下表达式进行转换：\n ${expression} \n `,
    'json_object',
    (res) => {
      try {
        const resObj = JSON.parse(res);
        return {
          flag: !!resObj.result,
          result: resObj.result,
        }
      }
      catch (err) {
        return {
          flag: false,
          message: err.message,
        }
      }
    });
  await llmClient.cleanup();
  return result;
};

export default convertExpression;

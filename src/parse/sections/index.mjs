'use strict';
import { generateBlock, generateEmptyBlock } from "../block/index.mjs";
import generateField from "../field/index.mjs";
import parseTrAttributes from './attributes/tr/index.mjs';
import parseTdAttributes from './attributes/td/index.mjs';
import { removeColon, singleObj2Array } from "../utils.js";
import {
  addCurrentBlockToSection,
  getColumnWidths,
  getMaxFormItemCount,
  getBlockTitle,
  extractListColumnIds,
  isListColumnHeader,
  isListColumnData,
  isBlockTitle,
} from './section-utils.mjs';

/**
 * 生成sections章节内容
 * @param {Object} ydpJson XML 文件转换生成的 JSON 内容
 * @param {String} pageName YDAP 页面名称
 * @param {Object} result 保存解析结果的对象
 * @returns {Object} 包含 sections 层的解析结果对象
 */
const generateSections = async (ydpJson, pageName, result) => {
  const formNode = ydpJson.page.form || {};
  const table = formNode.table || [];

  // 初始化数据
  let currentBlock = null; // 当前内容块
  let currentBlockTitle = ''; // 当前内容块标题
  let lastLabel = ''; // 文本标签

  // 创建一个默认章节
  const defaultSection = {
    title: "默认章节标题",
    blocks: []
  };
  result.sections = [defaultSection];

  // 获取列表列宽
  const thElements = table.th?.td || [];
  const columnWidths = getColumnWidths(thElements);

  // 遍历 tr 元素
  const trElements = table.tr || [];
  // 计算所有行中表单项的最大数量，用于确定每行需要渲染的表单个数
  const maxCount = getMaxFormItemCount(trElements);
  const maxFormCountInRow = maxCount > 3 ? 3 : maxCount;

  for (let trIndex = 0; trIndex < trElements.length; trIndex++) {
    const tr = trElements[trIndex];
    const nextTr = trIndex !== trElements.length - 1 ? trElements[trIndex + 1] : null;
    const tdElements = singleObj2Array(tr.td);
    const { ispage, dynamictag, EXT_IsEnable } = parseTrAttributes(tr);

    let rowFields = []; // 当前行表单输入项

    // 解析动态列表
    if (tr.EXT_LINETAG) {
      if (currentBlock) {
        addCurrentBlockToSection(defaultSection, currentBlock, result);
        currentBlockTitle = '';
      }
      const dynamicListBlock = await generateBlock('dynamicList', {
        tr,
        title: currentBlockTitle,
        pageName,
        columnWidths,
        ispage,
      });
      addCurrentBlockToSection(defaultSection, dynamicListBlock, result);
      currentBlockTitle = '';
      continue;
    }

    // 判断当前 tr 行是否为内容块标题行，并提取内容块标题
    if (isBlockTitle(tdElements)) {
      // 如果当前存在正在处理的内容块 且已提取到内容块标题，则将当前内容块添加到默认的章节中
      if (currentBlock && currentBlockTitle) {
        addCurrentBlockToSection(defaultSection, currentBlock, result);
        currentBlock = null;
      }
      // 提取当前 tr 行对应的内容块标题
      currentBlockTitle = getBlockTitle(tdElements);
      
      continue;
    }

    // 判断当前 tr 行是否为列表标题行
    const isListHeader = isListColumnHeader(tdElements);
    // 判断下一 tr 行是否为列表数据行
    const nextTrIsListData = nextTr ? isListColumnData(singleObj2Array(nextTr.td)) : false;
    // 如果当前 tr 行为列表标题行且下一 tr 行为列表数据行，则跳过这一行
    if (isListHeader && nextTrIsListData) continue;
    
    // 判断当前 tr 行是否为列表数据行
    const isList = isListColumnData(tdElements);

    for (let i = 0; i < tdElements.length; i++) {
      const td = tdElements[i];
      const { type, hidden, name, colspan, align } = parseTdAttributes(td);

      // 解析静态列表（从显示结构上认定为列表）
      if (isList) {
        // 检查当前列和上一列是否有值
        if (td.value && trIndex > 0) {
          const { listId = '', columnId } = extractListColumnIds(td.value);
          const prevTr = trElements[trIndex - 1];
          const prevTd = prevTr.td[i];
          const columnTitle = prevTd?.value ?? ''

          // 列表列定义
          const column = {
            id: columnId || td.value,
            label: columnTitle,
            width: columnWidths[i],
          };
          if (align) column.align = align;
          if (hidden) column.hidden = hidden;

          if (!currentBlock || currentBlock.type !== 'List') {
            if (currentBlock) {
              addCurrentBlockToSection(defaultSection, currentBlock, result);
              currentBlockTitle = '';
            }
            currentBlock = await generateBlock('staticList', {
              id: `list_${listId || result.sections.length + 1}`,
              title: currentBlockTitle,
              ispage,
            });
          }
          
          currentBlock.columns.push(column);
        }
      }
      // 解析 TD 文本标签
      else if (td.value) {
        if (lastLabel) {
          const field = generateField(td, tdElements, lastLabel, i);
          if (!field.hidden && hidden) field.hidden = hidden;
          rowFields.push(field);
          lastLabel = ''; // 清空上一个标签
        }
        else {
          lastLabel = removeColon(td.value);
        }
      }
      // 解析 Input 表单标签
      else if (td.input) {
        // 判断当前 TD 标签是否为按钮类型
        if (type === 'button') {
          if (!currentBlock) {
            currentBlock = generateEmptyBlock('Form', `form_${result.sections.length + 1}`, currentBlockTitle);
          }
          if (currentBlock && !currentBlock.buttons) {
            currentBlock.buttons = [];
          }
          
          // 生成按钮
          const button = generateField(td, tdElements, lastLabel, i);
          if (!button.visible && hidden) button.visible = hidden;
          currentBlock.buttons.push(button);
        }
        else {
          // 生成普通表单字段
          const field = generateField(td, tdElements, lastLabel, i);
          if (!field.hidden && hidden) field.hidden = hidden;
          rowFields.push(field);
        }

        lastLabel = ''; // 清空上一个标签
      }
    }

    // 计算当前行生成的表单数量
    const currentRowFieldCount = rowFields.length;

    // 如果当前行生成的表单数小于最大表单数，对最后一项进行补位处理
    if (currentRowFieldCount < maxFormCountInRow) {
      const keepSlotValue = maxFormCountInRow - currentRowFieldCount;
      const lastField = rowFields[rowFields.length - 1];
      if (lastField) {
        lastField.keepslot = keepSlotValue.toString();
      }
    }
    // 如果当前行生成的表单数大于最大表单数，对最后一项进行补位处理
    else if (currentRowFieldCount > maxFormCountInRow) {
      const remainder = currentRowFieldCount % maxFormCountInRow;
      if (remainder !== 0) {
        const keepSlotValue = maxFormCountInRow - remainder;
        const lastField = rowFields[rowFields.length - 1];
        if (lastField) {
          lastField.keepslot = keepSlotValue.toString();
        }
      }
    }

    // 将表单数据添加到已有内容块中
    if (rowFields.length > 0) {
      if (!currentBlock || currentBlock.type !== 'Form') {
        if (currentBlock) {
          addCurrentBlockToSection(defaultSection, currentBlock, result);
          currentBlockTitle = '';
        }
        currentBlock = generateEmptyBlock('Form', `form_${result.sections.length + 1}`, currentBlockTitle, {
          cols: maxFormCountInRow >= 3 ? '3' : maxFormCountInRow.toString()
        });
      }
      
      currentBlock.fields.push(...rowFields);
    }
  }

  // 遍历完所有 tr 行后添加当前内容块
  if (currentBlock) {
    defaultSection.blocks.push(currentBlock);
    currentBlock = null;
  }

  return result;
}

export default generateSections;
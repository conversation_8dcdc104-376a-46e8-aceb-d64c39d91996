/**
 * 解析 tr 标签的属性
 * @param {Object} trElement tr 标签对象
 * @returns {Object} 解析后的属性对象
 */
function parseTrAttributes(trElement) {
  const parsedAttributes = {};

  // 解析 height 行高属性
  if (trElement['@_height']) {
    parsedAttributes.height = parseInt(trElement['@_height'], 10);
  }

  // 解析 ispage 是否分页显示属性
  if (trElement['@_ispage']) {
    parsedAttributes.ispage = trElement['@_ispage'] === 'true';
  }

  // 解析 dynamictag 是否动态行属性
  if (trElement['@_dynamictag']) {
    parsedAttributes.dynamictag = trElement['@_dynamictag'] === 'true';
  }

  // 解析 EXT_IsEnable 是否启用扩展行属性
  if (trElement['@_EXT_IsEnable']) {
    parsedAttributes.EXT_IsEnable = trElement['@_EXT_IsEnable'] === 'true';
  }

  // 解析 row 行号属性
  if (trElement['@_row']) {
    parsedAttributes.row = parseInt(trElement['@_row'], 10);
  }

  // 解析 class 样式类名属性
  if (trElement['@_class']) {
    parsedAttributes.class = trElement['@_class'];
  }

  return parsedAttributes;
}

export default parseTrAttributes;
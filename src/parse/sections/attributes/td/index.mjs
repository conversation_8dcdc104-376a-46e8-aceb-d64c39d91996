import { generateHidden } from '../../../field/common/index.mjs';

/**
 * 解析 td 标签的属性
 * @param {Object} tdElement td 标签对象
 * @returns {Object} 解析后的属性对象
 */
function parseTdAttributes(tdElement) {
  const parsedAttributes = {};

  // 解析 span 是否跨列属性
  if (tdElement['@_span']) {
    parsedAttributes.span = tdElement['@_span'] === 'true';
  }

  // 解析 type 单元格类型属性，属性值包括common、text、selected、checkbox、radio、date、button等情况
  if (tdElement['@_type']) {
    parsedAttributes.type = tdElement['@_type'];
  }

  // 解析 hiddenExp 隐藏表达式属性
  if (tdElement['@_hiddenExp']) {
    parsedAttributes.hidden = generateHidden(tdElement['@_hiddenExp']);
  }

  // 解析 name 名称属性
  if (tdElement['@_name']) {
    parsedAttributes.name = tdElement['@_name'];
  }

  // 解析 rowspan 跨行数属性
  if (tdElement['@_rowspan']) {
    parsedAttributes.rowspan = parseInt(tdElement['@_rowspan'], 10);
  }

  // 解析 colspan 跨列数属性
  if (tdElement['@_colspan']) {
    parsedAttributes.colspan = parseInt(tdElement['@_colspan'], 10);
  }

  // 解析 col 列号属性
  if (tdElement['@_col']) {
    parsedAttributes.col = parseInt(tdElement['@_col'], 10);
  }

  // 解析 align 对齐方式属性
  if (tdElement['@_align']) {
    parsedAttributes.align = tdElement['@_align'];
  }

  return parsedAttributes;
}

export default parseTdAttributes;
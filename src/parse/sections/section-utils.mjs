/**
 * 将当前内容块添加到章节中
 * 如果当前章节存在，则直接将当前内容块添加到该章节的内容块数组中；
 * 如果当前章节不存在，则创建一个新的章节，设置默认章节标题，将当前内容块添加到新章节的内容块数组中，
 * 并将新章节添加到传入的 r 对象的 sections 数组中。
 *
 * @param {Object} currentSection - 当前章节对象。该对象应包含一个名为 'blocks' 的数组属性，用于存储内容块
 * @param {Object} currentBlock - 当前要添加的内容块对象。它可以是任意类型的对象，将被添加到章节的 'blocks' 数组中。
 * @param {Object} r - 包含章节数组的对象。该对象应包含一个名为 'sections' 的数组属性，用于存储章节对象。
 */
export const addCurrentBlockToSection = (currentSection, currentBlock, r) => {
  // 检查当前章节是否存在
  if (currentSection) {
    currentSection.blocks.push(currentBlock);
  }
  else {
    // 如果当前章节不存在，创建一个新的章节对象
    const newSection = {
      title: "默认章节标题",
      blocks: [currentBlock]
    };
    r.sections.push(newSection);
  }
}

/**
 * 获取所有tr行中表单数的最大值
 * 该函数用于遍历传入的所有 tr 行元素数组，统计每行中符合条件的表单元素数量，并找出其中的最大值。
 * 符合条件的表单元素是指：输入框（非按钮类型），或者值符合特定正则表达式规则的单元格内容。
 *
 * @param {Array} trElements - 包含所有 tr 元素的数组。每个 tr 元素代表表格中的一行
 * @returns {Number} - 所有行中表单数的最大值。如果传入的数组为空或没有符合条件的表单元素，返回 0
 */
export const getMaxFormItemCount = (trElements) => {
  // 定义简单字段的正则表达式，用于匹配形如 "=[字段名].[子字段名][数字]" 的值，例如："=customer.name[1]"
  const simpleFieldRegex = /^=([\w.]+)\[\d+\]$/;
  // 定义函数调用的正则表达式，用于匹配形如 "=函数名(参数)" 的值，例如："=getCustomerInfo(customer[1])"
  const functionCallRegex = /^=\w+\(([^)]+)\)$/;
  let maxFormCount = 0;

  trElements.some((tr) => {
    const tdElements = tr.td ? (Array.isArray(tr.td) ? tr.td : [tr.td]) : [];
    let formCount = 0;

    tdElements.some((td) => {
      if (
        (td.input && td.input['@_type'] !== 'button') ||
        (td.value && (simpleFieldRegex.test(td.value) || functionCallRegex.test(td.value)))
      ) {
        formCount++;
      }
      if (formCount >= 3) {
        return true;
      }
      return false;
    });

    maxFormCount = Math.max(maxFormCount, formCount);

    if (maxFormCount >= 3) {
      return true;
    }
    return false;
  });

  return maxFormCount;
};

/**
 * 获取内容块标题
 * 此函数的主要作用是从给定的表格行元素数组中，获取内容块的标题
 * 标题行的判定规则为：该行只有一个单元格有值，函数会返回这个有值单元格的内容作为标题。
 * 如果未找到符合条件的标题行或输入参数不符合要求，则返回空字符串。
 *
 * @param {Array} trElements - 表格行元素数组，数组中的每个元素代表表格的一行
 * @returns {String} - 如果成功找到符合条件的标题行，则返回标题行中唯一有值单元格的内容；如果未找到符合条件的标题行或输入参数不符合要求，则返回空字符串。
 */
export const getBlockTitle = (trElements) => {
  // 确保 trElements 存在且不为空
  if (!trElements || !Array.isArray(trElements) || trElements.length === 0) {
    return '';
  }

  return trElements[0].value || '';
};

/**
 * 获取列宽
 * @param {Array} thElements - th 元素数组，数组中的每个元素代表表格头部的一个单元格（th 元素）
 * @returns {Array} 列宽数组
 */
export const getColumnWidths = (thElements) => {
  // 遍历th元素数组，提取列宽
  return thElements.map(th => {
    if (th['#text'] && !isNaN(th['#text'])) {
      return Number(th['#text']);
    }

    return null;
  }).filter(width => width !== null);
}

/**
 * 从表达式中解析列表ID和列ID
 *
 * 该函数用于解析特定格式的表达式，从中提取出列表ID和列ID。这些表达式通常用于表示表格中的某一列与数据源中的某个字段相关联，
 * 并且该字段是一个列表类型的数据。支持解析三种常见的表达式结构：
 * 1. 简单结构：形如 `=addendnum_ds1.busipayamt[#]`，其中 `addendnum_ds1` 为列表ID，`busipayamt` 为列ID。
 * 2. 函数调用结构：形如 `=getOperName(addendnum_ds1.regop[#])`，其中 `addendnum_ds1` 为列表ID，`regop` 为列ID。
 * 3. 新结构：形如 `=columnId`，其中 `columnId` 为列ID，列表ID为空。
 *
 * @param {string} value - 输入的表达式字符串，应符合上述三种结构之一。
 * @returns {null|{listId: string, columnId: string}} - 如果输入的表达式匹配成功，则返回一个包含 `listId` 和 `columnId` 属性的对象；如果匹配失败，则返回一个空对象。
 */
export const extractListColumnIds = (value) => {
  // 匹配形如 =getOperName(addendnum_ds1.regop[#]) 的函数调用结构
  const functionMatch = value.match(/^=(\w+)\((\w+)\.(\w+)\[\#\]\)$/);
  if (functionMatch) {
    return { listId: functionMatch[2], columnId: functionMatch[3] };
  }

  // 匹配形如 =addendnum_ds1.busipayamt[#] 的简单结构
  const simpleMatch = value.match(/^=(\w+)\.(\w+)\[\#\]$/);
  if (simpleMatch) {
    return { listId: simpleMatch[1], columnId: simpleMatch[2] };
  }

  // 匹配形如 =columnId 的新结构
  const newMatch = value.match(/^=([a-zA-Z_]\w*)$/);
  if (newMatch) {
    return { listId: '', columnId: newMatch[1] };
  }

  return {};
};

/**
 * 判断当前行是否为列表列定义，满足以下条件之一即可：
 * 条件1：所有列 td.type 均为 common 类型，且至少有一个 td.value 包含 '#'，并且能够从 td.value 的值满足
 *       形如 `=getOperName(addendnum_ds1.regop[#])` 的函数调用结构或者形如 `=addendnum_ds1.busipayamt[#]` 的简单结构，
 *       能够从上述内容中提取出列表ID和列ID，满足至少一个 td.value 符合这个条件，认定该行为列表列定义。
 * 条件2：所有列 td.type 均为 common 类型，且这一行的 td.value 值要么为空，要么均为形如 `=flowname`、`=createuser` 的结构。
 *
 * @param {Array} tdElements - 当前行的表格单元格元素数组
 * @returns {Boolean} - 如果当前行满足静态列表行的条件，则返回 `true`，否则返回 `false`。
 */
export const isListColumnData = (tdElements) => {
  let hasCondition1 = false;
  let hasCondition2 = true;
  let hasNonEmptyValue = false;

  for (let td of tdElements) {
    if (td['@_type'] !== 'common') return false;

    const value = td.value;

    // 检查条件1：单元格值包含 '#'，并且可以提取出列表ID和列ID
    if (value && value.includes('#') && extractListColumnIds(value)) {
      hasCondition1 = true; // 满足条件1
    }

    // 检查条件2：单元格值要么为空，要么为形如 `=flowname` 或 `=createuser` 的结构
    if (value) {
      hasNonEmptyValue = true;
      if (!/^=[a-zA-Z_]\w*$/.test(value)) {
        hasCondition2 = false;
      }
    }
  }

  // 如果所有 td.value 均为空，则不符合要求
  if (!hasNonEmptyValue) return false;
  
  if (hasCondition1) return true;

  return hasCondition2;
}

/**
 * 判断当前行是否为列表列标题，满足以下所有条件：
 * 1、所有列 td.type 必须为 common
 * 2、所有列的 td.value 内容必须为空或者为中文字符或英文字符，不包含特殊字符如 =、[、# 等
 *
 * @param {Array} tdElements - 当前行的表格单元格元素数组
 * @returns {Boolean} - 如果当前行满足列表列标题的条件，则返回 `true`，否则返回 `false`。
 */
export const isListColumnHeader = (tdElements) => {
  // 正则表达式：匹配中文字符、英文字符（包括大小写）、冒号（包括大小写）、下划线或空值
  const validValuePattern = /^[\u4e00-\u9fa5a-zA-Z:_：]*$/;
  
  for (let td of tdElements) {
    // 条件1：所有列 td.type 必须为 common
    if (td['@_type'] !== 'common') return false;

    const value = td.value;

    // 条件2：所有列的 td.value 内容必须为空或者为中文字符或英文字符
    if (value && !validValuePattern.test(value)) return false;
  }
  
  return true;
};

/**
 * 判断当前 tr 行元素是否为内容标题行，满足以下条件：
 * 1、所有列 td.type 必须为 common 类型
 * 2、仅有一个 td.value 值不为空，且为中英文字符
 *
 * @param {Array} tdElements - 当前行的表格单元格元素数组
 * @returns {Boolean} - 如果当前行满足内容标题行的条件，则返回 `true`，否则返回 `false`。
 */
export const isBlockTitle = (tdElements) => {
  // 正则表达式：匹配中文字符或英文字符
  const validValuePattern = /^[\u4e00-\u9fa5a-zA-Z]+$/;

  let nonEmptyCount = 0; // 记录非空单元格的数量
  let isValid = true;    // 标志是否满足所有条件

  for (let td of tdElements) {
    // 条件1：所有列 td.type 必须为 common 类型
    if (td['@_type'] !== 'common') {
      isValid = false;
      break;
    }

    const value = td.value;

    if (value) {
      nonEmptyCount++;

      // 条件2：非空单元格的值必须为中英文字符
      if (!validValuePattern.test(value)) {
        isValid = false;
        break;
      }
    }
  }
  
  // 满足所有条件且仅有一个非空单元格
  return isValid && nonEmptyCount === 1;
};

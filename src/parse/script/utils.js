// 函数名到动作类型映射
const FUNCTION_TO_ACTION_MAP = {
  'validationSubmit': 'submit', // 提交表单
  'pageOnload': 'load', // 页面加载
  '_isIFsubmit': 'change', // 隐式提交之前执行
  '_IFSubmit': 'change', // 隐式提交之后执行
  'query': 'query', // 查询
  'change': 'change', // 修改
  'select': 'change', // 修改
};

/**
 * 根据函数名获取对应的动作类型
 * @param {string} funcName - 要查询的函数名，不区分大小写
 * @returns {string} - 若函数名匹配到 FUNCTION_TO_ACTION_MAP 中的键，返回对应的动作类型；若未匹配到，返回空字符串
 */
export const getActionTypeByFuncName = (funcName) => {
  const lowerCaseFuncName = funcName.toLowerCase();
  for (const key in FUNCTION_TO_ACTION_MAP) {
      if (key.toLowerCase() === lowerCaseFuncName) {
          return FUNCTION_TO_ACTION_MAP[key];
      }
  }
  return '';
};
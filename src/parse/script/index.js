import LLM from '../../llm/index.mjs';
import { defaultPromp } from "../../llm/prompts/script/index.js";
import { getActionTypeByFuncName } from './utils.js';

// 函数名到提示词映射
const FUNCTION_TO_PROMPT_MAP = {
  'default': defaultPromp, // 默认提示词
};

// 大模型实例
const llm = new LLM();

/**
 * Class ScriptParser
 * 该类用于解析 ydpJson 中的 script 标签内容
 * 采用单例模式，确保整个应用中只有一个实例存在
 */
class ScriptParser {
  constructor() {
    if (ScriptParser.instance) {
      return ScriptParser.instance;
    }
    ScriptParser.instance = this;
    Object.freeze(this);
    return this;
  }

  /**
   * 解析 ydpJson 中的 script 标签内容
   * @param {Object} ydpJson - 包含 script 标签的 JSON 对象
   * @returns {Object|null} - 解析后生成的属性和方法对象，如果没有有效内容则返回 null
   */
  async parse(ydpJson) {
    const content = ydpJson.page.form.script?.['@_value'] || '';
    const cleanContent = await this.cleanScript(content);

    if (cleanContent) {
      // 解析成多个函数和全局变量
      const { functions, globals } = this.parseScript(cleanContent);
      // 根据函数和全局变量生成对应的属性和方法
      const scriptResult = this.generateAttrsAndMethods(functions, globals);

      return scriptResult;
    }

    return null;
  }

  /**
   * 清理脚本内容
   * 去除脚本中的特殊转义字符，如 $enter$ 替换为换行符，反斜杠转义的单引号和双引号恢复正常，最后去除首尾空格。
   * @param {String} scriptContent - 原始脚本内容
   * @returns {String} - 清理后的脚本内容
   */
  cleanScript(scriptContent) {
    return scriptContent
      .replace(/\$enter\$/g, '\n')
      .replace(/\\'/g, '\'')
      .replace(/\\\"/g, '"')
      .trim();
  }

  /**
   * 将脚本分割成多个部分（如按函数），并提取全局变量
   * @param {String} script - 清理后的脚本内容，该字符串应包含可能的函数声明和全局变量声明。
   * @returns {Object} - 包含函数和全局变量的对象
   */
  parseScript(script) {
    // 提取函数
    const funcRegex = /function\s+([\w\d_]+)\s*\(([^)]*)\)\s*\{/g;
    const functions = [];
    let match;

    while ((match = funcRegex.exec(script)) !== null) {
      const name = match[1];
      const args = match[2];
      let bodyStart = funcRegex.lastIndex;
      let braceCount = 1;
      let i = bodyStart;
      for (; i < script.length; i++) {
        if (script[i] === '{') braceCount++;
        else if (script[i] === '}') braceCount--;
        if (braceCount === 0) break;
      }
      const body = script.slice(bodyStart, i);
      functions.push({ name, args, body });
      funcRegex.lastIndex = i + 1; // 继续下一个 function
    }

    // 移除函数体内容，只保留函数之间的部分
    let remainingScript = script;
    functions.forEach(func => {
      // 使用正则表达式移除函数体
      const functionBodyRegex = new RegExp(`function\\s+${func.name}\\s*\\([^)]*\\)\\s*\\{[\\s\\S]*?\\n\\}`, 'g');
      remainingScript = remainingScript.replace(functionBodyRegex, '');
    });
    remainingScript = remainingScript.replace(/\s+/g, ' ').trim();

    // 提取全局变量
    const globalRegex = /(?:var|let|const)\s+([\w\d_]+)\s*=\s*([^;]+);/g;
    const globals = {};
    let globalMatch;

    while ((globalMatch = globalRegex.exec(remainingScript)) !== null) {
      const varName = globalMatch[1];
      const varValue = globalMatch[2];
      globals[varName] = varValue;
    }

    return { functions, globals };
  }

  /**
   * 根据提取的函数和全局变量生成对应的属性和方法
   * @param {Object[]} functions - 提取到的函数信息数组
   * @param {Object} globals - 提取到的全局变量信息对象
   * @returns {Object} - 生成的属性和方法对象
   */
  async generateAttrsAndMethods(functions, globals) {
    const methods = {};

    // 并行处理所有函数的提取
    const extractResults = await Promise.all(functions.map(async (func) => {
      const { name, args, body } = func;
      const actions = await this.extractFunction(name, args, body);
      return { name, actions };
    }));

    for (const { name, actions } of extractResults) {
      console.log(name, actions);
      if (!actions) continue;
      const actionsArr = Array.isArray(actions) ? actions : [actions];
      methods[name] = actionsArr;
    }
    return { methods: methods };
  }

  /**
   * 从给定的函数名和函数体中提取结构化信息
   * @param {String} funcName - 函数的名称
   * @param {*} funcArgs - 函数的参数，支持任意类型。
   * @param {String} funcBody - 函数的主体内容
   * @returns {Promise<Object|null>} - 一个 Promise，解析为包含提取的结构化信息的对象，如果提取失败或函数体为空，则返回 null
   */
  async extractFunction(funcName, funcArgs, funcBody) {
    console.info('funcName', funcBody);
    funcBody = funcBody?.trim();

    // 只有当 funcBody 内容存在且不为空时才调用 LLM
    if (!funcBody) {
      return null;
    }

    let promptType = 'default';
    // if (/^on|handle/i.test(func.name)) promptType = 'event';
    // else if (/^get|set/i.test(func.name)) promptType = 'attribute';
    // else if (/^validate/i.test(func.name)) promptType = 'validate';

    // 根据函数名获取提示词
    const prompt = FUNCTION_TO_PROMPT_MAP[promptType];

    // 根据函数名获取动作类型
    const actionType = getActionTypeByFuncName(funcName);

    // 调用 LLM 进行解析
    const res = await llm.sendMessage([
      { 'role': 'system', 'content': prompt },
      {
        'role': 'user', 'content': `请提取以下SCRIPT语句的结构化信息:

\`\`\`function
${funcBody}
\`\`\`
注意：函数名为 ${funcName}，对应的 on 为 ${actionType}。根据传入的 ${actionType} 和相关信息决定生成的动作是否包含on属性。

`
      },
    ], [], 'json_object');


    let actions = null;
    try {
      actions = JSON.parse(res.choices[0].message.content);
    }
    catch (error) {
      console.log('[解析SCRIPT失败:', error.message || String(error), ']');
    }

    return actions;
  }
}

export default ScriptParser;
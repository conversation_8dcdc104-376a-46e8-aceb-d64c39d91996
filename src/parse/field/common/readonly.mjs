import { BOOLEAN_RANGE } from '../../../config/enum/index.js';
import expression from '../../expression/index.mjs';

// 判断是不是字符串的表达式
const reg = /^=\w+$/;

/**
 * 生成只读属性
 * @param {String} readonly 只读属性
 * @returns {String|null} 只读属性
 */
const generateReadonly = (readonly) => {
  if (!readonly || readonly.trim() === "") {
    return false;
  }
  
  switch (readonly) {
    case BOOLEAN_RANGE.TRUE:
    case BOOLEAN_RANGE.ONE:
      return BOOLEAN_RANGE.TRUE;
    case BOOLEAN_RANGE.FALSE:
    case BOOLEAN_RANGE.ZERO:
      return BOOLEAN_RANGE.FALSE;
    default:
      if (reg.test(readonly)) return `=%f.${readonly.slice(1)}`;
      return expression(readonly);
  }  
};

export default generateReadonly;

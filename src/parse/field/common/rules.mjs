/**
 * 处理表单校验规则
 * @param {Object} component 表单标签
 * @returns {Array} 校验规则数组
 */
const generateRules = (component) => {
  const rules = [];
  const { '@__required': required, '@_maxlength': maxlength, '@__maxlength': _maxlength, '@__minlength': _minlength } = component;

  // 必填校验
  if (required && required !== 'false') {
    rules.push({ name: 'required', value: 'true' });
  }
  // 最大长度校验
  if (maxlength || _maxlength) {
    rules.push({ name: 'maxLen', value: (maxlength || _maxlength) });
  }
  // 最小长度校验
  if (_minlength) {
    rules.push({ name: 'minLen', value: _minlength });
  }

  return rules;
}

export default generateRules;
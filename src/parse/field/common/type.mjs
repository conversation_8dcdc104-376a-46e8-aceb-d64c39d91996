/**
 * 根据类型确定 fieldType
 * @param {Object} component 表单标签
 * @returns {string} 字段类型
 */
const generateType = (component) => {
  const { '@_type': type, '@__type': subType } = component;
  
  switch (type) {
    case 'text':
      switch (subType) {
        case 'int':
          return 'Number';
        case 'number':
          return 'Number';
        case 'money':
          return 'Money';
        case 'date':
          return 'DateTime';
        default:
          return 'Input';
      }
    case 'select':
      return 'Select';
    case 'radio':
      return 'Radio';
    case 'checkbox':
      return 'Checkbox';
    case 'textarea':
      return 'TextArea';
    case 'password':
      return 'Password';
    case 'number':
      return 'Number';
    case 'date':
      return 'DateTime';
    case 'button':
      return 'Button';
    default:
      return 'Input';
  }
}

export default generateType;
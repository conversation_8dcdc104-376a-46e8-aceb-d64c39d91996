import expression from '../../expression/index.mjs';

// 判断是不是字符串的表达式
const reg = /^=\w+$/;

/**
 * 生成表单项的value值
 * @param {Object} component 表单标签或文本值
 * @returns {String} 解析后的表单项值
 */
const generateValue = async (component) => {
  const {
    '@_type': type,
    '@_initVal': initVal,
    '@_value': value,
    '@_name': id,
  } = component;

  let defaultValue;

  // 根据表单类型确定优先级
  if (['date', 'area', 'number'].includes(type)) {
    // 对于日期、区域和数字类型，优先使用initVal
    defaultValue = initVal || value;
  }
  else {
    // 对于其他类型，优先使用value
    defaultValue = value || initVal;
  }
  
  if (defaultValue) {
    // 如果不是表达式，直接返回原值
    if (!defaultValue.includes('=')) return defaultValue;
    // 如果是一个简单字符串表达式，则转成ydap格式的表达式
    if (reg.test(defaultValue)) return `=%f.${defaultValue.slice(1)}`;
    // 如果是复杂表达式，则调用模型生成表达式
    return await expression(defaultValue);
  }
  else {
    // 如果既没有initVal也没有value，默认取=%f.表单ID
    return `=%f.${id}`;
  }
};

export default generateValue;

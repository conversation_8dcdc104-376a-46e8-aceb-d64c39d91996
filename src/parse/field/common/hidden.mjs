import { BOOLEAN_RANGE } from '../../../config/enum/index.js';
import expression from '../../expression/index.mjs';

// 判断是不是字符串的表达式
const reg = /^=\w+$/;

/**
 * 生成隐藏表达式属性
 * @param {String} hidden 隐藏表达式属性
 * @returns {String|null} 隐藏表达式属性
 */
const generateHidden = (hidden) => {
  if (!hidden || hidden.trim() === "") {
    return false;
  }

  switch (hidden) {
    case BOOLEAN_RANGE.TRUE:
    case BOOLEAN_RANGE.ONE:
      return BOOLEAN_RANGE.TRUE;
    case BOOLEAN_RANGE.FALSE:
    case BOOLEAN_RANGE.ZERO:
      return BOOLEAN_RANGE.FALSE;
    default:
      if (reg.test(hidden)) return `=%f.${hidden.slice(1)}`;
      return expression(hidden);
  }
};

export default generateHidden;

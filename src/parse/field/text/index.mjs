import { removeColon } from "../../utils.js";

/**
 * 从给定的输入项提取表单ID
 * 支持的格式：
 * 1. 直接引用表单项：`=ds2.sumloan[1]`
 * 2. 函数调用中的表单项：`=getOrgName(selaccdatil_ds3.accinstcode1[1])`
 * 
 * @param {string} inputValue - 输入项的值
 * @returns {String|null} - 如果值符合格式则返回表单id，否则返回null
 */
const extractFormFieldId = (inputValue) => {
  // 检查输入是否为字符串
  if (typeof inputValue !== 'string') {
    return null;
  }
  
  // 正则表达式匹配直接引用表单项
  const simpleFieldRegex = new RegExp('^=([\\w.]+)\\[\\d+\\]$');
  // 正则表达式匹配函数调用中的表单项
  const functionCallRegex = new RegExp('^=\\w+\\(([^)]+)\\)$');

  // 尝试匹配直接引用表单项
  const simpleMatch = inputValue.match(simpleFieldRegex);
  if (simpleMatch) {
    // 提取字段ID
    const parts = simpleMatch[1].split('.');
    return parts[parts.length - 1];
  }

  // 尝试匹配函数调用
  const functionCallMatch = inputValue.match(functionCallRegex);
  if (functionCallMatch) {
    // 提取括号内的字段ID
    const innerMatch = functionCallMatch[1].match(/(\w+(?:\.\w+)+)\[\d+\]/);
    if (innerMatch) {
      const parts = innerMatch[1].split('.');
      return parts[parts.length - 1];
    }
  }
  
  return null;
};

/**
 * 生成文本显示表单
 * @param {Object} inputValue 文本内容
 * @param {string} label - 标签文本
 * @returns {Object} 文本显示组件
 */
const generateText = (inputValue, label) => {
  const fieldId = extractFormFieldId(inputValue); // 提取表单ID
  const fieldValue = fieldId ? `=%f.${fieldId}` : inputValue; // 如果提取到表单ID，则格式化显示值
  
  return {
    type: 'Text',
    label: label ? removeColon(label) : '',
    value: fieldValue,
  };
};

export default generateText;
/**
 * 生成选择类型表单的特殊属性
 * @param {Object} component 表单组件
 * @returns {Object} 选择类型特殊属性
 */
const generateSelectProperties = (component) => {
  const {
    '@_options': options,
    '@_multiple': multiple,
    '@_placeholder': placeholder,
  } = component;

  return {
    // 选项数据
    ...(options ? { options } : {}),
    // 是否多选
    ...(multiple ? { multiple } : {}),
    // 占位符
    ...(placeholder ? { placeholder } : {}),
    // 选择类型标识
    selectType: 'select'
  };
};

export default generateSelectProperties; 
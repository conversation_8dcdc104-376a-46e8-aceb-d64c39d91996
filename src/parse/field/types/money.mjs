import { resolveExpression } from './utils.mjs';

/**
 * 生成金额类型表单的特殊属性
 * @param {Object} component 表单组件
 * @returns {Object} 金额类型特殊属性
 */
const generateMoneyProperties = async (component) => {
  const {
    '@__delNum': delNum,
    '@__maxvalue': maxValueExp,
    '@__minvalue': minValueExp,
    '@_allowNegative': allowNegative,
    '@_name': id,
  } = component;

  // 处理可能包含表达式的值
  let maxValue = await resolveExpression(maxValueExp);
  let minValue = await resolveExpression(minValueExp);

  // 如果允许负数，且最小值为空，则设置为一个很大的负数
  if (allowNegative === 'true' && !minValue) {
    minValue = '-999999999';
  }

  return {
    // 精度设置
    ...(delNum ? { precision: delNum } : {}),
    // 最小值（支持表达式）
    ...(minValue ? { min: minValue } : {}),
    // 最大值（支持表达式）
    ...(minValue ? { max: maxValue } : {}),
    // 后缀单位
    suffix: '元'
  };
};

export default generateMoneyProperties; 
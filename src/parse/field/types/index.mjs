import generateMoneyProperties from './money.mjs';
import generateDateProperties from './date.mjs';
import generateNumberProperties from './number.mjs';
import generateTextProperties from './text.mjs';
import generateSelectProperties from './select.mjs';
import generateTextareaProperties from './textarea.mjs';

/**
 * 根据表单类型生成对应的特殊属性
 * @param {Object} component 表单组件
 * @param {String} type 表单类型
 * @returns {Object} 特殊属性对象
 */
const generateTypeProperties = async (component, type, subType) => {
  switch (type) {
    case 'Money':
      return await generateMoneyProperties(component);
    case 'date':
      return generateDateProperties(component);
    case 'number':
      return await generateNumberProperties(component);
    case 'text':
    case 'Text':
      return await generateTextProperties(component);
    case 'select':
    case 'Select':
      return generateSelectProperties(component);
    case 'textarea':
    case 'Textarea':
      return await generateTextareaProperties(component);
    default:
      return {};
  }
};

export default generateTypeProperties; 
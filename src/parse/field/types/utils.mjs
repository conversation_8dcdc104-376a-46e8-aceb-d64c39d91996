import expression from '../../expression/index.mjs';

// 判断是不是字符串的表达式
const reg = /^=\w+$/;

/**
 * 处理可能包含表达式的值
 * @param {String} value 原始值
 * @returns {String} 处理后的值
 */
const resolveExpression = async (value) => {
  if (!value) return undefined;
  
  // 如果不是表达式，直接返回原值
  if (!value.includes('=')) return value;
  
  // 如果是一个简单字符串表达式，则转成ydap格式的表达式
  if (reg.test(value)) return `=%f.${value.slice(1)}`;
  
  // 如果是复杂表达式，则调用模型生成表达式
  return await expression(value);
};


export { resolveExpression }; 
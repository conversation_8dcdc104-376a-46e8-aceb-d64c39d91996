import { resolveExpression } from './utils.mjs';

/**
 * 生成文本域类型表单的特殊属性
 * @param {Object} component 表单组件
 * @returns {Object} 文本域类型特殊属性
 */
const generateTextareaProperties = async (component) => {
  const {
    '@_rows': rows,
    '@_cols': cols,
    '@_maxlength': maxlength,
    '@_placeholder': placeholder,
    '@_name': id,
  } = component;

  // 处理可能包含表达式的值
  const maxLength = await resolveExpression(maxlength, id);

  return {
    // 行数
    ...(rows ? { rows } : {}),
    // 列数
    ...(cols ? { cols } : {}),
    // 最大长度（支持表达式）
    ...(maxLength ? { maxlength: maxLength } : {}),
    // 占位符
    ...(placeholder ? { placeholder } : {}),
    // 文本域类型标识
    textareaType: 'textarea'
  };
};

export default generateTextareaProperties; 
import { resolveExpression } from './utils.mjs';

/**
 * 生成文本类型表单的特殊属性
 * @param {Object} component 表单组件
 * @returns {Object} 文本类型特殊属性
 */
const generateTextProperties = async (component) => {
  const {
    '@_maxlength': maxlength,
    '@_minlength': minlength,
    '@_pattern': pattern,
    '@_placeholder': placeholder,
    '@_name': id,
  } = component;

  // 处理可能包含表达式的值
  const maxLength = await resolveExpression(maxlength, id);
  const minLength = await resolveExpression(minlength, id);

  return {
    // 最大长度（支持表达式）
    ...(maxLength ? { maxlength: maxLength } : {}),
    // 最小长度（支持表达式）
    ...(minLength ? { minlength: minLength } : {}),
    // 正则表达式
    ...(pattern ? { pattern } : {}),
    // 占位符
    ...(placeholder ? { placeholder } : {}),
    // 文本类型标识
    textType: 'text'
  };
};

export default generateTextProperties; 
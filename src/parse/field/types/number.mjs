import { resolveExpression } from './utils.mjs';

/**
 * 生成数字类型表单的特殊属性
 * @param {Object} component 表单组件
 * @returns {Object} 数字类型特殊属性
 */
const generateNumberProperties = async (component) => {
  const {
    '@__maxvalue': maxValueExp,
    '@__minvalue': minValueExp,
    '@__delNum': delNum,
    '@_allowNegative': allowNegative,
    '@_name': id,
  } = component;

 // 处理可能包含表达式的值
 let maxValue = await resolveExpression(maxValueExp);
 let minValue = await resolveExpression(minValueExp);

 // 如果允许负数，且最小值为空，则设置为一个很大的负数
 if (allowNegative === 'true' && !minValue) {
   minValue = '-999999999';
 }

  return {
    // 最小值（支持表达式）
    ...(minValue ? { min: minValue } : {}),
    // 最大值（支持表达式）
    ...(maxValue ? { max: maxValue } : {}),
    // 精度
    ...(delNum ? { precision: delNum } : {}),
    // 数字类型标识
    numberType: 'number'
  };
};

export default generateNumberProperties; 
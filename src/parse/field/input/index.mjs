import { trimAndConvert } from "../../utils.js";
import { generateReadonly, generateHidden, generateValue, generateType, generateRules } from "../common/index.mjs";
import generateTypeProperties from "../types/index.mjs";

/**
 * 生成表单项输入项
 * @param {Object} component 表单标签或文本值
 * @param {String} label 文本标签
 * @returns {Object} 表单项
 */
const generateInput = async (component, label) => {
  // 处理表单输入项
  const {
    '@_name': id,
    '@_type': type,
    '@__type': subType,
    '@__required': required,
    '@_readOnlyExp': readOnly,
    '@_hiddenExp': hidden,
  } = component;
  // 表单字段定义
  let field = { id, type: generateType(component) };

  // 表单字段默认值
  const defaultValue = await generateValue(component);
  // 必输条件，只读条件，隐藏条件属性转换
  const processedRequired = trimAndConvert(required);
  const processedReadOnly = generateReadonly(readOnly);
  const processedHidden = generateHidden(hidden);
  // 表单校验规则
  const rules = generateRules(component);

  // 根据表单类型生成特殊属性
  const typeProperties = await generateTypeProperties(component, type, subType);

  field = {
    ...field,
    // 字段标签, 给隐藏输入框添加默认标签
    label: label || (processedHidden ? '隐藏框_' + id : ''),
    // 字段ID
    id,
    // 字段默认值
    value: defaultValue || `=%f.${id}`,
    // 只读条件
    ...(processedReadOnly ? { readonly: processedReadOnly } : {}),
    // 隐藏条件
    ...(processedHidden ? { hidden: processedHidden } : {}),
    // 表单校验规则
    ...(rules.length > 0 ? { rules } : {}),
    // 类型特定属性
    ...typeProperties,
  };

  return field;
}

export default generateInput;
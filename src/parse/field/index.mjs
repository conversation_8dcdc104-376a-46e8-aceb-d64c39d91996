import generateText from './text/index.mjs';
import generateInput from './input/index.mjs';
import generateButton from './button/index.mjs';

/**
 * 生成表单项
 * @param {Object} component 当前组件对象，包含组件类型和值等信息
 * @param {Object[]} components 当前行所有组件对象数组
 * @param {String} label 当前组件的标签文本
 * @param {Number} index 当前组件在行中的索引
 * @returns {Object} 返回生成的表单组件
 *
 * 该函数根据传入的组件类型生成对应的表单组件：
 * 1. 如果组件类型为 'common'，生成文本组件。
 * 2. 如果组件类型为 'button'，生成按钮组件。
 * 3. 如果组件类型为 'edoc'，处理电子档案逻辑（待实现）。
 * 4. 默认情况下，生成输入框组件。
 */
const generateField = (component, components, label, index) => {
  const {
    '@_type': type, // 组件类型
  } = component;

  switch (type) {
    case 'common':
      // 生成文本显示组件
      return generateText(component.value, label);
    case 'button':
      // 生成按钮组件
      return generateButton(component.input, components);
    case 'edoc':
      // TODO: 处理电子档案逻辑
      // 这里可以添加电子档案相关的处理逻辑
      break;
    default:
      // 默认生成输入框组件
      return generateInput(component.input, label);
  }
};

export default generateField;
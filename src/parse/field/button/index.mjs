/**
 * 生成按钮
 * @param {Object} component 表单标签
 * @param {Object[]} components 当前行所有组件对象数组
 * @returns {Object} 按钮对象
 */
function generateButton(component, components) {
  const eventType = component.event?.['@_eventType'] || '';
  const eventScript = component.event?.['@_eventScript'] || '';
  const label = component['@_value'] || '';

  const button = {
    label,
    visible: "true",
    type: "default", // 默认类型
    actions: []
  };

  // TODO: 解析按钮的动作配置
  if (eventScript) {
    button.actions.push({
      on: "click",
      type: eventScript,
    });
  }

  return button;
}

export default generateButton;
import { singleObj2Array } from "./utils.js";

/**
 * 生成页面标题
 * @param {Object} ydpJson - xml文件转换生成的json内容
 * @returns {string} 页面标题
 */
/**
 * 生成页面标题
 * @param {Object} ydpJson - xml文件转换生成的json内容
 * @returns {string} 页面标题
 */
const getTitle = (ydpJson) => {
  // 获取表单节点
  const form = ydpJson.page.form || [];
  // 获取表单上配置的页面标题
  let pageTitle = form['@_title'] || '';

  // 如果没有配置页面标题，则尝试从table中提取标题
  if (!pageTitle) {
    const trElements = form.table.tr || [];

    for (let i = 0; i < trElements.length; i++) {
      const tr = trElements[i];
      const tdElements = singleObj2Array(tr.td);

      let isPageTitle = true;
      let hasValueCount = 0;
      let titleValue = '';

      for (const td of tdElements) {
        if (td['@_type'] !== 'common') {
          isPageTitle = false;
          break;
        }
        
        if (td.value) {
          hasValueCount++;
          titleValue = td.value;
        }
        
        if (hasValueCount > 1) break;
      }

      if (isPageTitle && hasValueCount === 1) {
        pageTitle = titleValue;
        form.table.tr.splice(i, 1);
        break;
      }
    }
  }

  return pageTitle;
};


/**
 * 解析生成页面层
 * @param {Object} ydpJson xml文件转换生成的json内容
 * @returns {Object} 页面对象
 */
const generatePage = (ydpJson) => {
  // 解析生成页面标题
  const pageTitle = getTitle(ydpJson);

  const ydap = {
    title: pageTitle,
    sections: [],
    actions: [],
  };

  return ydap;
};

export default generatePage;
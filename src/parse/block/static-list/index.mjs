/**
 * 生成静态列表
 * @param {Object} props 生成静态列表的参数
 * @returns {Object} 列表内容块对象
 */
const generateStaticList = async (props) => {
  const { id, title, columns = [], ispage, autoquery = 'true', shownumber = 'true', update = 'false' } = props;

  return {
    type: 'List',
    id,
    title,
    columns,
    autoquery,
    shownumber,
    paging: ispage ? 'true' : 'false',
    update,
  };
};

export default generateStaticList;
/**
 * 生成空块对象的函数
 * @param {string} type - 块的类型，目前支持 'Form' 和 'List'
 * @param {string} id - 块的唯一标识符
 * @param {string} title - 块的标题
 * @param {Object} [options={}] - 可选配置项，不同类型的块有不同的默认配置
 * @returns {Object} - 根据类型生成的空块对象，如果类型不支持则返回空对象
 */
const generateEmptyBlock = (type, id, title = '', options = {}) => {
  switch (type) {
    case 'Form':
      return {
        type: 'Form',
        id,
        title,
        cols: options.cols || '3',
        fields: [],
        buttons: []
      };
    case 'List':
      return {
        type: 'List',
        id,
        title,
        columns: [],
        autoquery: options.autoquery || 'false',
        shownumber: options.shownumber || 'false',
        paging: options.paging || 'false',
        update: options.update || 'false',
      };
    default:
      return {};
  }
}

export default generateEmptyBlock;

import { YDP_TAGS } from "../../../config/enum/index.js";
import generateActions from "../../action/index.js";
/**
 * 解析列内容
 * @param {string} rowText 列文本内容
 * @returns {Array} 列选项
 */
const parseRowContent = (rowText) => {
  if (!rowText.includes('<dt>const</>')) return [];
  const contentMatch = rowText.match(/<content>(.*?)<\/>/);
  if (!contentMatch || !contentMatch[1]) return [];
  
  return contentMatch[1].split(',').map(pair => {
    const [value, label] = pair.split(':').map(str => str.replace(/"/g, '').trim());
    return { value, label };
  });
};

/**
 * 生成列动作
 * @param {Object} row 当前行数据
 * @param {Object} tr 当前 tr 元素
 * @param {string} pageName YDAP 页面名称
 * @returns {Array} 列动作
 */
const generateColumnActions = async (row, tr, pageName) => {
  if (!row['#text']?.includes('<dt>sql</>')) return [];
  
  const { [`${pageName}_${tr.EXT_LINETAG['@_extname']}_${row['@_EXT_fdname']}`]: { action } } = await generateActions({
    ...row,
    listId: tr.EXT_LINETAG['@_extname'],
    datasourcename: tr.EXT_LINETAG['@_data_source']
  }, YDP_TAGS.ROW, pageName);
  return [action];
};

/**
 * 生成动态列表
 * @param {Object} props 生成动态列表的参数
 * @returns {Object} 列表内容块对象
 */
const generateDynamicList = async (props) => {
  const { tr, title, pageName, columnWidths, ispage } = props;
  const { '@_extname': extname } = tr.EXT_LINETAG;
  const rows = tr.EXT_LINETAG.EXT_fdListDic.row || [];

  // 是否所有列都为只读（不可编辑）
  let areAllColumnsReadOnly = true;
  // 列表动作配置
  let listActions = [];

  const columns = await Promise.all(rows.map(async (row, index) => {
    if (row['@_EXT_fdIsEdit'] !== 'false') {
      areAllColumnsReadOnly = false;
    }

    const rowText = row['#text'] || '';
    const options = parseRowContent(rowText);
    const actions = await generateColumnActions(row, tr, pageName);

    return {
      id: row['@_EXT_fdname'], // 列ID
      label: row['@_EXT_fddesc'], // 列标题
      hidden: row['@_EXT_fdIsHidden'], // 列字段隐藏
      width: columnWidths[index],
      ...(row['@_EXT_fdIsEdit'] !== 'false' && { readonly: row['@_EXT_fdIsEdit'] }), // 列字段是否可编辑
      ...(options.length > 0 && { options, type: "select" }), // 列类型
      ...(actions.length > 0 && { actions }), // 列字典
    };
  }));

  if (tr.EXT_LINETAG.EXT_strSql) {
    const { [`${pageName}_${extname}`]: { action } } = await generateActions(tr.EXT_LINETAG, YDP_TAGS.EXT_strSql, pageName);
    listActions = [action];
  }

  return {
    type: 'List',
    id: extname,
    title,
    columns,
    autoquery: 'true',
    shownumber: 'true',
    paging: ispage ? 'true' : 'false',
    update: areAllColumnsReadOnly ? "false" : "true",
    ...(listActions.length > 0 && { actions: listActions }),
  };
};

export default generateDynamicList;
import generateEmptyBlock from "./empty-block.mjs";
import generateStaticList from "./static-list/index.mjs";
import generateDynamicList from "./dynamic-list/index.mjs";

/**
 * 生成内容块的函数
 *
 * @param {string} type - 内容块的类型
 * @param {Object} props - 其他参数项
 * @returns {Object} - 返回生成的内容块对象，如果类型不匹配则返回一个空对象
 */
const generateBlock = async (type, props) => {
  switch (type) {
    case 'Form':
      break;
    case 'staticList':
      return await generateStaticList(props);
    case 'dynamicList':
      return await generateDynamicList(props);
    default:
      return {};
  }
}

export { generateBlock, generateEmptyBlock };


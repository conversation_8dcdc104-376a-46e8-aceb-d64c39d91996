import extractSQLActions from "./sql.js";

/**
 * 提取 EXT_fdListDic 标签中 row 标签的相关配置，将其转换成动作
 * @param {Object} sqlTag - 包含 EXT_fdListDic 标签中 row 标签相关信息的对象
 * @param {string} pageName - 页面名称，用于生成唯一的动作键
 * @returns {Promise<Object|null>} - 返回一个 Promise，解析结果可能是包含动作信息的对象，也可能是 null
 */
const extractExtRowSqlActions = async (sqlTag, pageName) => {
  const { '@_EXT_fdname': columnId, listId, '#text': text, 'datasourcename': sourceName } = sqlTag;
  const actionKey = `${pageName}_${listId}_${columnId}`;
  
  // 提取 <content> 标签内的 SQL 语句
  const contentMatch = text.match(/<content>(.*?)<\/>/);
  if (contentMatch && contentMatch[1]) {
    const sql = contentMatch[1];

    // 提取sql中的信息并转换成动作
    const action = await extractSQLActions(sql, actionKey);
    if (!action) return null;

    return {
      [actionKey]: {
        ...action,
        // 动作以外需要保存到excel的信息
        sourceName,
      }
    };
  }
  else {
    return null;
  }
};

export default extractExtRowSqlActions;

import { YDP_TAGS } from "../../config/enum/index.js";
import extractIframeActions from "./iframe.js";
import extractDataSourceActions from "./data-source.js";
import extractExtStrSqlActions from "./ext-strsql.js";
import extractExtRowSqlActions from "./ext-row.js";
import ActionManager from "./ActionManager.js";

const generateActions = async (ydpJson, type, pageName) => {
  let actions = {};

  switch (type) {
    case YDP_TAGS.DATA_SOURCE:
      // 转换规则：
      actions = await extractDataSourceActions(ydpJson, pageName);
      break;
    case YDP_TAGS.IFRAMES:
      actions = await extractIframeActions(ydpJson, pageName);
      break;
    case YDP_TAGS.EXT_strSql:
      actions = await extractExtStrSqlActions(ydpJson, pageName);
      break;
    case YDP_TAGS.ROW:
      actions = await extractExtRowSqlActions(ydpJson, pageName);
      break;
  }

  // 将生成的actions添加到ActionManager中
  if (actions && Object.keys(actions).length > 0) {
    const actionManager = ActionManager.getInstance();
    actionManager.addActions(actions);
    console.log(`[generateActions] 已将${Object.keys(actions).length}个actions添加到ActionManager`);
  }

  return actions;
};

export default generateActions;

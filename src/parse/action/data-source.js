import { YDP_TAGS } from "../../config/enum/index.js";
import { singleObj2Array } from "../utils.js";
import extractSQLActions from './sql.js';

/**
 * 提取data_source标签的相关配置，转换成动作
 * 主要转换成2类动作：
 *  1. sql语句中无动态内容，则在页面加载时直接执行
 *  2. sql语句中包含动态内容，需要在动态内容更新后执行
 * @param ydpJson
 * @param pageName
 * @returns {Promise<void>}
 */
const extractDataSourceActions = async (ydpJson, pageName) => {
  const sqlTags = singleObj2Array(ydpJson?.[YDP_TAGS.SQL] ?? []);

  const structuredSQLArr = await Promise.all(
    sqlTags.map(async (sqlTag) => {
      const { '#text': text, '@_key': dataSourceKey, '@_datasourcename': sourceName } = sqlTag;
      const actionKey = `${pageName}_${dataSourceKey}`;

      // 提取sql中的信息并转换成动作
      const action = await extractSQLActions(text, actionKey);
      if (!action) return null;

      return {
        [actionKey]: {
          ...action,
          // 动作以外需要保存到excel的信息
          sourceName,
        }
      };
    })
  );

  // 使用reduce将数组中的对象合并成一个对象
  return structuredSQLArr.reduce((acc, sqlActions) => {
    return { ...acc, ...sqlActions };
  }, {});
};

export default extractDataSourceActions;

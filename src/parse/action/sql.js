import SQL_PROMPTS from "../../llm/prompts/sql.js";
import LLM from '../../llm/index.mjs';
import { YDAP_ACTION_TYPES } from '../../config/enum/index.js';
import { generateEventCode } from "../utils.js";

const llm = new LLM();

/**
 * 利用模型提取sql信息
 * @param sql
 * @returns {Promise<*|null>}
 */
const extractSQL = async (sql) => {
  const sqlContent = sql?.trim();

  // 只有当 SQL 内容存在且不为空时才调用 LLM
  if (!sqlContent) {
    return null;
  }

  const res = await llm.sendMessage([
    { 'role': 'system', 'content': SQL_PROMPTS },
    {
      'role': 'user', 'content': `请提取以下SQL语句的结构化信息:

\`\`\`sql
${sqlContent}
\`\`\``
    },
  ], [], 'json_object');

  let sqlInfo = null;
  try {
    sqlInfo = JSON.parse(res.choices[0].message.content);
  } catch (error) {
    console.log('[解析SQL失败:', error.message || String(error), ']');
  }

  return sqlInfo;
};

/**
 * 生成动作对象
 *
 * @param sqlInfo
 * @returns {Promise<Object>}
 */
const generateAction = async (sqlInfo) => {
  const { dynamic_content = [] } = sqlInfo;
  // 如果sql中不包含动态内容，则在页面加载时执行
  const isExecuteOnLoad = dynamic_content.every(item => item.function_name !== 'getPoolValue');

  const action = {
    isExecuteOnLoad,
    sqlInfo,
    // 实际YDAP中要用到的动作配置
    'action': {
      type: YDAP_ACTION_TYPES.AJAX,
      api: generateEventCode(),
    }
  };

  let params = null;

  // 拼接动作上传参
  if (dynamic_content.length > 0) {
    params = dynamic_content.reduce((p, item) => {
      if (item.field_name === 'null') return p;
      p[item.field_name] = `=%f.${item.field_name}`;
      return p;
    }, {});
  }

  if (params) action.action.params = params;

  return action;
}

const extractSQLActions = async (sql) => {
  // 提取sql信息
  const sqlInfo = await extractSQL(sql);
  if (!sqlInfo) return null;

  // 根据sql信息生成action
  return await generateAction(sqlInfo);
};

export default extractSQLActions;

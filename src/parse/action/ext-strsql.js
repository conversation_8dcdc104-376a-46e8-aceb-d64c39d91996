import extractSQLActions from "./sql.js";

/**
 * 提取 EXT_strSql 标签的相关配置，转换成动作
 * @param {Object} sqlTag - 包含 EXT_strSql 标签相关信息的对象
 * @param {string} pageName - 页面名称，用于生成唯一的动作键
 * @returns {Promise<Object|null>} - 返回一个 Promise，当成功提取并转换动作时，解析为包含动作信息的对象；若提取动作失败，则解析为 null
 */
const extractExtStrSqlActions = async (sqlTag, pageName) => {
  const { 'EXT_strSql': text, '@_extname': extStrSqlKey, '@_data_source': sourceName } = sqlTag;
  const actionKey = `${pageName}_${extStrSqlKey}`;

  // 提取sql中的信息并转换成动作
  const action = await extractSQLActions(text, actionKey);
  if (!action) return null;

  return {
    [actionKey]: {
      ...action,
      // 动作以外需要保存到excel的信息
      sourceName,
    }
  };
};

export default extractExtStrSqlActions;

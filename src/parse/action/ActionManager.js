/**
 * Action管理器 - 单例模式
 * 负责管理页面中的所有action，包括存储和获取
 */
class ActionManager {
  // 私有静态实例
  static #instance = null;

  // 私有属性
  #actions = new Map(); // 存储所有action
  #loadTimeActions = new Map(); // 页面加载时执行的action
  #eventTriggeredActions = new Map(); // 事件触发或数据总线变更后执行的action

  /**
   * 私有构造函数，防止外部直接实例化
   */
  constructor() {
    if (ActionManager.#instance) {
      throw new Error('ActionManager是单例类，请使用getInstance()方法获取实例');
    }
  }

  /**
   * 获取单例实例
   * @returns {ActionManager} ActionManager实例
   */
  static getInstance() {
    if (!ActionManager.#instance) {
      ActionManager.#instance = new ActionManager();
    }
    return ActionManager.#instance;
  }

  /**
   * 添加action
   * @param {string} actionId - action的唯一标识
   * @param {Object} actionConfig - action配置对象
   * @param {string} actionConfig.name - action名称
   * @param {string} actionConfig.type - action类型 (sql, iframe等)
   * @param {boolean} actionConfig.isExecuteOnLoad - 是否在页面加载时执行
   * @param {Object} actionConfig.sqlInfo - SQL信息(当type为sql时)
   * @param {Array} actionConfig.triggers - 触发条件(当isExecuteOnLoad为false时)
   */
  addAction(actionId, actionConfig) {
    if (!actionId || typeof actionId !== 'string') {
      throw new Error('actionId必须是非空字符串');
    }

    if (!actionConfig || typeof actionConfig !== 'object') {
      throw new Error('actionConfig必须是对象');
    }

    const { isExecuteOnLoad = false } = actionConfig;

    // 存储到总的actions集合中
    this.#actions.set(actionId, {
      id: actionId,
      ...actionConfig,
      createdAt: new Date()
    });

    // 根据执行时机分类存储
    if (isExecuteOnLoad) {
      this.#loadTimeActions.set(actionId, actionConfig);
    } else {
      this.#eventTriggeredActions.set(actionId, actionConfig);
    }

    console.log(`[ActionManager] 添加action: ${actionId}, 加载时执行: ${isExecuteOnLoad}`);
  }

  /**
   * 批量添加actions
   * @param {Object} actionsMap - actions映射对象，key为actionId，value为actionConfig
   */
  addActions(actionsMap) {
    if (!actionsMap || typeof actionsMap !== 'object') {
      throw new Error('actionsMap必须是对象');
    }

    Object.entries(actionsMap).forEach(([actionId, actionConfig]) => {
      this.addAction(actionId, actionConfig);
    });
  }

  /**
   * 获取指定action
   * @param {string} actionId - action的唯一标识
   * @returns {Object|null} action配置对象，不存在时返回null
   */
  getAction(actionId) {
    return this.#actions.get(actionId) || null;
  }

  /**
   * 获取所有actions
   * @returns {Map} 所有actions的Map对象
   */
  getAllActions() {
    return new Map(this.#actions);
  }

  /**
   * 获取页面加载时执行的actions
   * @returns {Map} 页面加载时执行的actions
   */
  getLoadTimeActions() {
    return new Map(this.#loadTimeActions);
  }

  /**
   * 获取事件触发的actions
   * @returns {Map} 事件触发的actions
   */
  getEventTriggeredActions() {
    return new Map(this.#eventTriggeredActions);
  }

  /**
   * 根据类型获取actions
   * @param {string} type - action类型
   * @returns {Array} 指定类型的actions数组
   */
  getActionsByType(type) {
    const result = [];
    this.#actions.forEach((action) => {
      if (action.type === type) {
        result.push(action);
      }
    });
    return result;
  }

  /**
   * 根据触发条件获取actions
   * @param {string} trigger - 触发条件
   * @returns {Array} 匹配触发条件的actions数组
   */
  getActionsByTrigger(trigger) {
    const result = [];
    this.#eventTriggeredActions.forEach((action, actionId) => {
      if (action.triggers && action.triggers.includes(trigger)) {
        result.push(this.#actions.get(actionId));
      }
    });
    return result;
  }

  /**
   * 移除指定action
   * @param {string} actionId - action的唯一标识
   * @returns {boolean} 是否成功移除
   */
  removeAction(actionId) {
    const action = this.#actions.get(actionId);
    if (!action) {
      return false;
    }

    // 从所有集合中移除
    this.#actions.delete(actionId);
    this.#loadTimeActions.delete(actionId);
    this.#eventTriggeredActions.delete(actionId);

    console.log(`[ActionManager] 移除action: ${actionId}`);
    return true;
  }

  /**
   * 清空所有actions
   */
  clearAllActions() {
    this.#actions.clear();
    this.#loadTimeActions.clear();
    this.#eventTriggeredActions.clear();
    console.log('[ActionManager] 清空所有actions');
  }
}

export default ActionManager;

'use strict';
import _ from 'lodash';

/**
 * 去除字符串首尾空格并进行必要的转换
 * @param {string} value 输入值
 * @returns {string|*} 处理后的值
 */
export const trimAndConvert = (value) => {
  if (typeof value === 'string') {
    const trimmed = value.trim();
    if (trimmed === '1==1' || trimmed === '1') {
      return 'true';
    }
    return trimmed;
  }
  return value;
}

/**
 * 去除标签中的冒号
 * @param {String} label 标签
 * @returns {String} 去除冒号后的标签
 */
export const removeColon = (label) => {
  if (typeof label === 'string') {
    return label.replace(/：|:$/, '');
  }
  return label;
}

/**
 * 将单个对象转换为数组
 * 当fast-xml-parser 返回一个对象时，将其转换为数组
 * @param {Object|Array} obj - 单个对象或数组
 * @returns {Array} 转换后的数组
 */
export const singleObj2Array = (obj) => {
  if (Array.isArray(obj)) return obj;
  return [obj];
};


/**
 * 合并多个对象，如果遇到相同的属性且其值都为数组，则将这些数组连接起来。
 * 对于非数组或不同类型的相同属性，lodash 会采用其默认的合并策略（通常是后者覆盖前者）。
 *
 * @param {...Object} sources - 需要合并的一个或多个源对象。第一个对象会被作为基础，后续对象会合并到其上。
 * @returns {Object} 合并后的新对象。
 */
export const mergeAndConcatArrays = (...sources) => {
  // 定义自定义合并器
  const customizer = (objValue, srcValue) => {
    // 检查当前合并的两个值是否都是数组
    if (Array.isArray(objValue) && Array.isArray(srcValue)) {
      // 如果都是数组，则将它们连接起来并返回
      return objValue.concat(srcValue);
    }
    // 对于其他类型的值，返回 undefined 让 lodash 使用其默认合并行为
    return undefined;
  };

  // 使用 mergeWith 将所有源对象合并到一个空对象上，并应用自定义合并器
  // 第一个参数 {} 是目标对象，确保我们返回的是一个新对象，而不是修改原始对象
  return _.mergeWith({}, ...sources, customizer);
}


let eventCodeNum = 1;
/**
 * 生成6位数的事件代码字符串
 * 格式：T7 + 4位数字（不足4位前面补0）
 * 每次调用时eventCodeNum自动递增1
 * @returns {string} 6位数的事件代码，如 "T70001", "T70002" 等
 */
export const generateEventCode = () => {
  const code = `T7${eventCodeNum.toString().padStart(4, '0')}`;
  eventCodeNum++;
  return code;
}


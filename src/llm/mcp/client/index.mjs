import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { addLog } from '../../../log/index.mjs';
import { LOG_TYPE } from '../../../config/enum/index.js';
import LLM from '../../index.mjs';
import systemPrompt from '../../prompts/system/system.mjs';
import { codeSearchServices } from '../server/index.mjs';

// 失败最大重试次数
const MAX_RETRIES = process.env.LLM_RETRY_MAX_TIMES;

class MCPClient {
  #mcp = null;
  #llm = null;
  // 传输层
  #transport = null;
  // 历史消息
  #messages = [];
  // 系统提示词
  #systemPrompt = '';
  // 可用工具
  #tools = [];
  // 重试次数
  #retryCount = 0;

  constructor(sysPrompt = systemPrompt) {
    this.#llm = new LLM();
    this.#systemPrompt = sysPrompt;
    this.#mcp = new Client({
      name: 'mcp-client',
      version: '1.0.0',
    });
    this.#resetMessages();
  }

  #resetMessages() {
    this.#messages = [{
      role: 'system',
      content: this.#systemPrompt,
    }];
  }

  async connect() {
    // 创建传输层
    this.#transport = new StdioClientTransport(codeSearchServices);
    // 连接到服务器
    await this.#mcp.connect(this.#transport);
    // 获取可用的工具
    this.#tools = await this.#getTools();
  }

  /**
   * 获取可用的工具
   * @returns {Array}
   */
  async #getTools() {
    try {
      const toolsResult = await this.#mcp.listTools();

      addLog(toolsResult, LOG_TYPE.GET_TOOLS);

      // 将格式转换为open ai支持的格式
      return toolsResult.tools.map((tool) => ({
        type: "function",
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.inputSchema || {},
        },
      }));
    } catch (error) {
      addLog(error, LOG_TYPE.GET_TOOLS_ERROR);
      console.error('获取可用的工具失败:', error);
      return [];
    }
  }

  /**
   * 执行工具调用
   * @param {string} toolName
   * @param {Object} toolArgs
   * @returns {Array}
   */
  async #callTool(toolName, toolArgs) {
    addLog({
      toolName,
      toolArgs,
    }, LOG_TYPE.TOOL_CALL);

    try {
      const result = await this.#mcp.callTool({
        name: toolName,
        arguments: toolArgs,
      });

      addLog(result, LOG_TYPE.TOOL_CALL_RESPONSE);

      return result;
    } catch (error) {
      addLog(error, LOG_TYPE.TOOL_CALL_ERROR);
      throw new Error(error.message || String(error));
    }
  }

  /**
   * 发送消息
   * @param {string} message
   * @param {string|null} response_format
   * @param {Function} response_handle 校验返回结果是否符合标准 r => { flag: boolean, result: any }
   * @returns {string|null}
   */
  async sendMessage(
    message,
    response_format = null,
    response_handle,
  ) {
    try {
      // 校验是否超过了最大重试次数
      this.#retryCount++;
      if (this.#retryCount > MAX_RETRIES) return;
      console.log(`第${this.#retryCount}次尝试`);

      if (this.#retryCount === 1) {
        this.#messages.push({
          role: 'user',
          content: message,
        });
      }

      // 处理结果函数
      const resHandler = async (msg) => {
        if (typeof response_handle === 'function') {
          const { flag, result } = response_handle(msg.content);
          // 校验通过，返回格式化后的值
          if (flag) return result;
          // 校验未通过，需要再次调用模型
          else {
            this.#messages.push(
              msg,
              {
                user: 'user',
                content: '生成的结果不正确，请检查并重新生成',
              }
            );
            return this.sendMessage(message, response_format, response_handle);
          }
        }
        // 不校验，则直接返回内容
        return msg.content;
      };

      // 发送消息到 llm，让模型决定使用哪些工具
      const response = await this.#llm.sendMessage(this.#messages, this.#tools, response_format);
      const responseMsg = response?.choices?.[0]?.message;

      // 添加模型回复消息
      if (responseMsg) {
        return await resHandler(responseMsg);
      }

      // 处理工具调用
      if (responseMsg.tool_calls && responseMsg.tool_calls.length > 0) {
        // 添加工具调用到历史消息
        this.#messages.push(responseMsg);

        responseMsg.tool_calls.forEach(toolCall => {
          if (toolCall.type !== 'function') return;

          const toolName = toolCall.function.name;
          const toolArgs = JSON.parse(toolCall.function.arguments || "{}");

          // 执行工具调用
          try {
            const toolResult = this.#callTool(toolName, toolArgs);
            const content = typeof toolResult === 'object' ? JSON.stringify(toolResult) : toolResult;

            this.#messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content,
            });
          } catch (error) {
            this.#messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: `工具调用失败: ${error.message || String(error)}`,
            });
          }
        });
      }

      // 调用模型，对工具执行结果进行解析
      try {
        const response = await this.#llm.sendMessage(this.#messages, [], response_format);
        const content = response.choices[0].message;

        return await resHandler(content);
      }
      catch (error) {
        console.log(`[根据mcp-server执行结果，调用模型失败: ${error.message || String(error)}]`);
        return null;
      }
    } catch (error) {
      addLog(error, LOG_TYPE.LLM_ERROR);
      console.error('LLM 发送消息失败:', error);
      return null;
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    // 清空历史消息
    this.#resetMessages();

    if (this.#transport) {
      try {
        this.#transport.close();
        this.#transport = null;
        console.log('传输层已关闭');
      } catch (error) {
        console.error('关闭传输层失败:', error);
      }
    }

    if (this.#mcp) {
      try {
        this.#mcp.close();
        this.#mcp = null;
        console.log('MCP 客户端已关闭');
      } catch (error) {
        console.error('关闭 MCP 客户端失败:', error);
      }
    }
  }
}

export default MCPClient;

import OpenAI from "openai";
import dotenv from "dotenv";
import { addLog } from "../log/index.mjs";
import { LOG_TYPE } from "../config/enum/index.js";

dotenv.config();

class LLM {
  #openai;
  #model;

  constructor() {
    const {
      baseURL,
      apiKey,
      model
    } = this.#getConfig();

    this.#openai = new OpenAI({
      baseURL,
      apiKey,
      model,
    });
    this.#model = model;
  }

  // 验证环境变量配置
  #validateConfig() {
    if (!process.env.LLM_BASE_URL) {
      throw new Error('LLM_BASE_URL 未配置');
    }
    if (!process.env.LLM_API_KEY) {
      throw new Error('LLM_API_KEY 未配置');
    }
    if (!process.env.LLM_MODAL_NAME) {
      throw new Error('LLM_MODAL_NAME 未配置');
    }
  }

  // 加载环境变量配置
  #getConfig() {
    this.#validateConfig();

    return {
      baseURL: process.env.LLM_BASE_URL,
      apiKey: process.env.LLM_API_KEY,
      model: process.env.LLM_MODAL_NAME,
    }
  }

  /**
   * 发送消息
   * @param {Array} messages
   * @param {Array} tools 工具列表
   * @param {string|null} response_format 响应格式
   * @returns {string|null}
   */
  async sendMessage(messages = [], tools = [], response_format) {
    const msg = {
      model: this.#model,
      messages,
    }
    if (tools.length > 0) {
      msg.tools = tools;
      msg.tool_choice = "auto";
    }
    if (response_format) msg.response_format = { 'type': response_format };

    try {
      addLog(msg, LOG_TYPE.LLM_REQUEST);
      const response = await this.#openai.chat.completions.create(msg);
      addLog(response, LOG_TYPE.LLM_RESPONSE);
      return response;
    } catch (error) {
      addLog(error, LOG_TYPE.LLM_ERROR);
      console.error('LLM 发送消息失败:', error);
      return null;
    }
  }

  getModel() {
    return this.#model;
  }
}

export default LLM;

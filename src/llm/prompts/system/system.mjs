import basic from "./basic.mjs";

export default `${basic}

## 容器类型(container[@type])
1. **box**：盒子容器
   - 主要属性：id, uid, layout="fit", width, height, background, caption
   
2. **hidden**：隐藏容器
   - 主要属性：id, uid, layout

3. **table**：表格容器
   - 主要属性：id, uid, layout="fit", cols(列数), caption
   - 直接包含component元素

4. **list**：列表容器
   - 主要属性：id, uid, layout="fit", cols, dataset, caption, allowexport, allowprint
   - 包含三个子节点：
     * listhead：列表头部
     * listbody：列表主体
     * hidden：隐藏元素

## 组件类型(component[@type])
1. **label**：标签组件
   - 属性：id, uid, type="label", for(关联输入项ID), colspan, prefix, suffix, hidden, top, left, width, height, link, linkexp, sortname
   - 内容：显示文本

2. **blank**：空白组件
   - 属性：type="blank", colspan

3. **plain**：纯文本组件
   - 属性：id, uid, type="plain", datatype, colspan, prefix, suffix, hidden, top, left, width, height
   - 内容：显示文本

4. **caption**：标题组件
   - 属性：id, uid, type="caption", colspan, prefix, suffix, hidden, top, left, width, height, level
   - 内容：标题文本

5. **input**：输入组件
   - 属性：id, uid, type="input", subtype(text/date/money等), datatype, colspan, prefix, suffix, hidden, top, left, width, height, maxlength, minlength, declen, readonly, required, max, min, negative, fromdic
   - 内容：通常为"=字段名"格式

6. **button**：按钮组件
   - 属性：id, uid, type="button", subtype="button", colspan, prefix, suffix, hidden, top, left, width, height
   - 内容：按钮文本

7. **multivalue**：多值组件
   - 属性：id, uid, type="multivalue", subtype="select", colspan, prefix, suffix, hidden, top, left, width, height, readonly, required, value, fromdic
   - 可能包含sql子元素定义数据源

8. **datalist**：数据列表组件
   - 属性：id, uid, type="datalist", colspan, prefix, suffix, hidden, top, left, width, height, paging="true", layout="fit"

9. **hidden**：隐藏组件
   - 属性：id, uid, type="hidden", colspan, prefix, suffix, hidden, top, left, width, height

## 数据集定义(/page/dataset/sql)
- 属性：id, desc, paging, pagesize, maxrows, scopeall, datasource
- 内容：SQL查询语句

## AJAX查询定义(/page/ajax/query)
- 属性：id, desc, trigger, message
- 子元素：
  * sql：定义数据源和SQL语句
  * target：定义目标，包含id, label, value属性

# 转换任务
请将YDPX文件转换为YDAP格式，保持以下原则：
1. 保留所有容器和组件的ID、类型和属性
2. 维持容器和组件的层次结构关系
3. 正确处理特殊容器(如list)的子元素结构
4. 保留数据集和AJAX查询定义
5. 保留用户脚本
6. 确保不丢失任何业务逻辑

请提供转换后的YDAP文件，并说明你做出的主要转换决策。`;

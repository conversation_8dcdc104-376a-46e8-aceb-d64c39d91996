'use strict';
import { promises as fs, readFileSync } from 'fs';
import { basename, join, dirname } from 'path';
import { fileURLToPath } from 'url';
import iconv from 'iconv-lite';
import { XMLParser } from 'fast-xml-parser';
import XlsxTemplate from 'xlsx-template';
import * as logger from './logger.js';
import parse from './parse/index.js';
import makeYdap from './make-ydap.js';
import Analyzer from './analyze/index.js';
import { initLogSession, saveSessionLogs } from './log/index.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

//Excel模板
const TEMPLATE = readFileSync(join(__dirname, '../ydp-template.xlsx'));

/**
 * 转换 ydp 文件
 * @param {String} ydpPath ydp文件完整路径
 * @param {Object} stats 统计数据
 */
export default async (ydpPath, stats) => {
  logger.info('正在转换', ydpPath);

  // 初始化日志会话
  initLogSession();

  try {
    // ydp文件名部分
    const baseName = basename(ydpPath);
    // ydap文件名称
    const ydapName = `Page${baseName.replace(/\.ydp$/i, '')}.ydap`;
    // 读取 ydp 文件内容
    const buffer = await fs.readFile(ydpPath);
    const ydp = iconv.decode(buffer, 'gbk');

    // 从 xml 转为 json
    const options = {
      ignoreAttributes: false,
    };
    const parser = new XMLParser(options);
    const ydpJson = parser.parse(ydp);

    // 解析内容元素
    const ydap = await parse(ydpJson, baseName.replace(/\.ydp$/i, ''));
    // 转换生成 ydap 文件
    await makeYdap(ydpPath, ydap, ydapName);

    // 分析未成功转换的内容
    const analyzer = new Analyzer(ydpJson);
    const { unconvertedTagAttrs, unhandledFormEvents, javascript, dmlEventActions } = await analyzer.analyze();

    // 输出结果到 Excel 模板
    const template = new XlsxTemplate(TEMPLATE);
    template.substitute('总体说明', { baseName, ydapName });
    template.substitute('未处理的标签和属性', { elements: unconvertedTagAttrs });
    template.substitute('未处理的表单事件', { elements: unhandledFormEvents });
    template.substitute('大模型处理结果', { elements: dmlEventActions });
    template.substitute('复杂逻辑处理', { javascript });
    const file = template.generate({ type: 'nodebuffer' });
    await fs.writeFile(ydpPath.replace(/\.ydp$/i, '') + '.xlsx', file);

    stats.successCount++;
  }
  catch (err) {
    stats.errorCount++;
    logger.error(`转换文件时出错`, ydpPath, err);
  }
  finally {
    stats.totalCount++;
    // 保存当前会话的所有日志到文件
    saveSessionLogs();
  }
};

import fs from 'fs';
import iconv from 'iconv-lite';

/**
 * 读取文件
 * @param {string} path 文件路径
 * @returns {string} 文件内容
 */
export const readFile = (path) => {
  const buffer = fs.readFileSync(path);
  return iconv.decode(buffer, 'gbk');
};

/**
 * 写入文件
 * @param {string} path 文件路径
 * @param {string} content 文件内容
 */
export const writeFile = (path, content) => {
  fs.writeFileSync(path, content);
};

// YDAP标签名称枚举（类似YDP_TAGS）
export const YDAP_TAGS = {
  // 页面根层级标签（对应YDP的page、form、table等）
  PAGE: 'page',           // YDAP页面根对象
  SECTION: 'section',     // 章节（对应YDP的tr）
  BLOCK: 'block',         // 内容块（对应YDP的td）
  FIELD: 'field',         // 表单字段（对应YDP的input）
  COLUMN: 'column',       // 列表列（对应YDP的td）
  BUTTON: 'button',       // 按钮
  MODAL: 'modal',         // 模态框
  ACTION: 'action',       // 动作
  RULE: 'rule',           // 验证规则（对应YDP的验证属性）
  OPTION: 'option',       // 选项（对应YDP的option）

  // 集合标签（包含多个子元素的容器）
  SECTIONS: 'sections',   // 章节集合
  BLOCKS: 'blocks',       // 内容块集合
  FIELDS: 'fields',       // 字段集合
  COLUMNS: 'columns',     // 列集合
  BUTTONS: 'buttons',     // 按钮集合
  MODALS: 'modals',       // 模态框集合
  ACTIONS: 'actions',     // 动作集合
  RULES: 'rules',         // 验证规则集合
  OPTIONS: 'options',     // 选项集合

  // 数据源相关标签（对应YDP的data_source、sql等）
  DATASOURCE: 'datasource',

  // 共享内容块相关标签
  COMPONENT: 'component',
  PARAMS: 'params',
  PROPS: 'props'
};

// YDAP内容块类型枚举
export const YDAP_BLOCK_TYPES = {
  // 基础内容块类型
  FORM: 'Form',
  LIST: 'List',
  TREE: 'Tree',
  TIPS: 'Tips',
  COMPARE: 'Compare',
  GENERAL: 'General',
  CUSTOM: 'Custom',
  SYSTEM: 'System',
  INPUT: 'Input'  // 兼容旧版本
};

// YDAP表单控件类型枚举
export const YDAP_FORM_TYPES = {
  // 基础文本类控件
  TEXT: 'Text',
  INPUT: 'Input',
  TEXTAREA: 'TextArea',
  PASSWORD: 'Password',

  // 数字类控件
  NUMBER: 'Number',
  MONEY: 'Money',
  NUMBER_RANGE: 'NumberRange',
  MONEY_RANGE: 'MoneyRange',

  // 选择类控件
  SELECT: 'Select',
  RADIO: 'Radio',
  CHECKBOX: 'Checkbox',
  SWITCH: 'Switch',
  TAG_SELECT: 'TagSelect',
  TREE_SELECT: 'TreeSelect',

  // 日期时间控件
  DATETIME: 'DateTime',
  DATE_TIME_RANGE: 'DateTimeRange',

  // 特殊控件
  AUTO_COMPLETE: 'AutoComplete',
  DEVICE: 'Device',
  IMAGE: 'Image'
};

// YDAP动作类型枚举
export const YDAP_ACTION_TYPES = {
  // 基础动作
  AJAX: 'ajax',
  ALERT: 'alert',
  CONFIRM: 'confirm',
  DELAY: 'delay',

  // 表单动作
  FORM_RESET: 'formReset',
  FORM_VALIDATE: 'formValidate',
  SET_FIELD_VALUE: 'setFieldValue',
  SET_FIELDS_VALUE: 'setFieldsValue',
  SET_MODAL_FIELD_VALUE: 'setModalFieldValue',
  SET_MODAL_FIELDS_VALUE: 'setModalFieldsValue',

  // 列表动作
  LIST_ADD: 'listAdd',
  LIST_CLEAR: 'listClear',
  LIST_QUERY: 'listQuery',
  LIST_SEARCH: 'listSearch',
  LIST_EXPORT: 'listExport',
  LIST_IMPORT: 'listImport',
  LIST_PRINT: 'listPrint',
  LIST_BATCH_DEL: 'listBatchDel',
  LIST_BATCH_SAVE: 'listBatchSave',
  LIST_UNCHECK_ALL: 'listUncheckAll',
  LIST_UPDATE_AJAX: 'listUpdateAjax',
  LIST_UPDATE_SOURCE: 'listUpdateSource',
  LIST_CHECK: 'listCheck',

  // 页面导航动作
  NEW_PAGE: 'newPage',
  NEW_WINDOW: 'newWindow',
  NEW_FLOW: 'newFlow',
  CLOSE_ALL_TABS: 'closeAllTabs',
  CLOSE_CURRENT_TAB: 'closeCurrentTab',

  // 模态框动作
  SHOW_MODAL: 'showModal',

  // 流程动作
  SUBMIT_FLOW: 'submitFlow',
  REJECT: 'reject',

  // 系统动作
  LOGOUT: 'logout',
  LOCAL_AUTH: 'localAuth',
  IMPLICIT_LINK: 'implicitLink',

  // 树形控件动作
  RELOAD_TREE: 'reloadTree',

  // 对比组件动作
  TOGGLE_COMPARE_SHOW_CHANGED_ONLY: 'toggleCompareShowChangedOnly',
  SET_COMPARE_SOURCE: 'setCompareSource',
  IDENTIFY_COMPARE: 'identifyCompare',

  // 特殊动作
  EDOC: 'edoc',
  SCAN_FACE: 'scanFace',
  TRIGGER_EVENT: 'triggerEvent',
  AJAX_CLEAR: 'ajaxClear',
  BATCH_ERROR_CLEAR: 'batchErrorClear',
  LOOP_QUERY: 'loopQuery',
  ASYNC_ACTIONS: 'asyncActions'
};

// YDAP事件类型枚举
export const YDAP_EVENT_TYPES = {
  // 页面级事件
  ON_LOAD: 'load',
  ON_SHOWN: 'shown',
  ON_UNLOAD: 'unload',

  // 表单控件事件
  ON_CHANGE: 'change',
  ON_BLUR: 'blur',
  ON_FOCUS: 'focus',
  ON_CLICK: 'click',

  // 模态框事件
  ON_MODAL_SHOWN: 'shown',
  ON_MODAL_CLOSE: 'close',
  ON_MODAL_CLOSED: 'closed'
};

// YDAP内容块属性枚举
export const YDAP_BLOCK_ATTRIBUTES = {
  // 通用属性
  ID: 'id',
  TYPE: 'type',
  TITLE: 'title',
  BUTTONS: 'buttons',
  ACTIONS: 'actions',

  // 表单内容块属性
  FIELDS: 'fields',
  LAYOUT: 'layout',

  // 列表内容块属性
  COLUMNS: 'columns',
  DATASOURCE: 'datasource',
  CHECKBOX: 'checkbox',
  ROW_BUTTONS: 'rowbuttons',
  EDIT_MODE: 'editmode',
  BATCH_SAVE: 'batchsave',
  INSERT: 'insert',
  DELETE: 'delete',
  BATCH_DELETE: 'batchdelete',
  UPDATE: 'update',
  IMPORT: 'import',
  EXPORT: 'export',
  PRINT: 'print',

  // 树形内容块属性
  READONLY: 'readonly',

  // 自定义组件属性
  COMPONENT: 'component',
  PROPS: 'props'
};

// YDAP表单字段属性枚举
export const YDAP_FIELD_ATTRIBUTES = {
  // 基础属性
  ID: 'id',
  TYPE: 'type',
  LABEL: 'label',
  VALUE: 'value',
  PLACEHOLDER: 'placeholder',
  READONLY: 'readonly',
  HIDDEN: 'hidden',
  REQUIRED: 'required',

  // 验证规则
  RULES: 'rules',
  VALIDATE_FIRST: 'validatefirst',

  // 选项相关
  OPTIONS: 'options',
  OPTION_GROUP: 'optiongroup',
  VIEW_GROUP: 'viewgroup',

  // 数字相关
  PRECISION: 'precision',
  MIN: 'min',
  MAX: 'max',
  STEP: 'step',
  ALLOW_NEGATIVE: 'allowNegative',

  // 文本相关
  MAXLENGTH: 'maxlength',
  ROWS: 'rows',
  MAX_ROWS: 'maxrows',

  // 布局相关
  SPAN: 'span',
  COLSPAN: 'colspan',
  INLINE: 'inline',

  // 事件相关
  ACTIONS: 'actions',
  ASYNC_ACTIONS: 'asyncactions',

  // 特殊属性
  SUFFIX: 'suffix',
  PREFIX: 'prefix',
  CLASSNAME: 'classname',
  DOUBLE_CHECK: 'doublecheck',
  BUTTON_TEXT: 'buttontext',
  BUTTON_CDT: 'buttoncdt',
  KEEP_SLOT: 'keepslot',
  COMPARE_COND: 'comparecond',
  COMPARE_SOURCE: 'comparesource',
  RARE_WORD: 'rareword',
  API: 'api',
  MASK: 'mask',
  MASK_CHAR: 'maskchar',
  NO_BR: 'nobr',
  CHECK_ALL: 'checkall',
  SHOW_CHECK: 'showcheck',
  SHOW_PLAIN: 'showplain',
  SHOW_COMPLEXITY: 'showcomplexity',
  ALLOW_CLEAR: 'allowclear',
  MODE: 'mode',
  API_TYPE: 'apitype',
  PARAMS: 'params',
  AMOUNT: 'amount',
  DIRTY: 'dirty',
  TRIGGER_LENGTH: 'triggerlength',
  VALUE_KEY: 'valuekey',
  OPTION_KEY: 'optionkey',
  COL_WIDTH: 'colwidth',
  COL_LABEL: 'collabel',
  DEVICE: 'device',
  FIELD: 'field'
};

// YDAP按钮类型枚举
export const YDAP_BUTTON_TYPES = {
  // 基础按钮
  BUTTON: 'button',
  SUBMIT: 'submit',
  RESET: 'reset',

  // 列表按钮
  LIST_ADD: 'listAdd',
  LIST_BATCH_SAVE: 'listBatchSave',
  LIST_EXPORT: 'listExport',
  LIST_IMPORT: 'listImport',
  LIST_PRINT: 'listPrint'
};

// YDAP模态框属性枚举
export const YDAP_MODAL_ATTRIBUTES = {
  // 基础属性
  ID: 'id',
  TITLE: 'title',
  SIZE: 'size',
  WIDTH: 'width',
  HEIGHT: 'height',
  CLOSABLE: 'closable',
  MASK_CLOSABLE: 'maskClosable',

  // 内容
  BLOCKS: 'blocks',

  // 事件
  SHOWN_ACTIONS: 'shownActions',
  ACTIONS_SHOWN: 'actionsshown',
  CLOSE_ACTIONS: 'closeActions',
  ACTIONS_CLOSE: 'actionsclose',
  CLOSED_ACTIONS: 'closedActions',
  ACTIONS_CLOSED: 'actionsclosed'
};

// YDAP验证规则类型枚举
export const YDAP_VALIDATION_TYPES = {
  REQUIRED: 'required',
  LENGTH: 'len',
  MIN_LENGTH: 'minLen',
  MAX_LENGTH: 'maxLen',
  MIN_VALUE: 'min',
  MAX_VALUE: 'max',
  PATTERN: 'pattern',
  EMAIL: 'email',
  URL: 'url',
  PHONE: 'phone',
  ID_CARD: 'idCard'
};

// YDAP列表编辑模式枚举
export const YDAP_LIST_EDIT_MODES = {
  NONE: 'none',
  INLINE: 'inline',
  MODAL: 'modal',
  BATCH: 'batch'
};

// YDAP列表列属性枚举
export const YDAP_LIST_COLUMN_ATTRIBUTES = {
  // 基础属性
  ID: 'id',
  LABEL: 'label',
  WIDTH: 'width',
  TYPE: 'type',
  ALIGN: 'align',
  FIXED: 'fixed',
  HIDDEN: 'hidden',
  FIELD_HIDDEN: 'fieldhidden',
  ELLIPSIS: 'ellipsis',

  // 数据相关
  VALUE: 'value',
  FORMAT: 'format',
  PRECISION: 'precision',
  DECLEN: 'declen',

  // 选项相关
  OPTIONS: 'options',
  OPTION_LABEL: 'optionlabel',
  OPTION_GROUP: 'optiongroup',
  VIEW_GROUP: 'viewgroup',

  // 编辑相关
  INLINE: 'inline',
  READONLY: 'readonly',
  PLACEHOLDER: 'placeholder',
  RULES: 'rules',

  // 排序和搜索
  SORT: 'sort',
  PINYIN: 'pinyin',

  // 特殊属性
  SUFFIX: 'suffix',
  PREFIX: 'prefix',
  COLSPAN: 'colspan',
  DOUBLE_CHECK: 'doublecheck',
  BUTTON_TEXT: 'buttontext',
  BUTTON_CDT: 'buttoncdt',
  BUTTONS: 'buttons',
  KEEP_SLOT: 'keepslot',
  CLASSNAME: 'classname',
  COMPARE_COND: 'comparecond',
  COMPARE_SOURCE: 'comparesource',
  RARE_WORD: 'rareword',
  API: 'api',
  MASK: 'mask',
  MASK_CHAR: 'maskchar',
  ROWS: 'rows',
  MAX_ROWS: 'maxrows',
  NO_BR: 'nobr',
  MIN: 'min',
  MAX: 'max',
  STEP: 'step',
  CHECK_ALL: 'checkall',
  SHOW_CHECK: 'showcheck',
  SHOW_PLAIN: 'showplain',
  SHOW_COMPLEXITY: 'showcomplexity',
  ALLOW_CLEAR: 'allowclear',
  MODE: 'mode',
  API_TYPE: 'apitype',
  PARAMS: 'params',
  AMOUNT: 'amount',
  DIRTY: 'dirty',
  TRIGGER_LENGTH: 'triggerlength',
  VALUE_KEY: 'valuekey',
  OPTION_KEY: 'optionkey',
  COL_WIDTH: 'colwidth',
  COL_LABEL: 'collabel',
  DEVICE: 'device',
  FIELD: 'field',
  ACTIONS: 'actions'
};

// YDAP按钮属性枚举
export const YDAP_BUTTON_ATTRIBUTES = {
  // 基础属性
  ID: 'id',
  TYPE: 'type',
  TEXT: 'text',
  ICON: 'icon',
  SIZE: 'size',
  SHAPE: 'shape',
  LOADING: 'loading',
  DISABLED: 'disabled',
  HIDDEN: 'hidden',

  // 样式相关
  CLASSNAME: 'classname',
  STYLE: 'style',
  THEME: 'theme',

  // 事件相关
  ACTIONS: 'actions',
  CONFIRM: 'confirm',
  CONDITION: 'condition',

  // 特殊属性
  POSITION: 'position',
  ORDER: 'order'
};

// YDAP章节属性枚举
export const YDAP_SECTION_ATTRIBUTES = {
  // 基础属性
  ID: 'id',
  TITLE: 'title',
  ICON: 'icon',
  HIDDEN: 'hidden',
  COLLAPSIBLE: 'collapsible',
  COLLAPSED: 'collapsed',

  // 内容
  BLOCKS: 'blocks',
  BUTTONS: 'buttons',

  // 布局相关
  LAYOUT: 'layout',
  SPAN: 'span',
  GUTTER: 'gutter'
};

// YDAP 按钮主题枚举
export const YDAP_BUTTON_THEMES = {
  PRIMARY: 'primary',
  DEFAULT: 'default',
  DASHED: 'dashed',
  TEXT: 'text',
  LINK: 'link'
};

// YDAP 选择模式枚举
export const YDAP_SELECT_MODES = {
  DEFAULT: 'default',
  MULTIPLE: 'multiple',
  TAGS: 'tags',
  COMBOBOX: 'combobox'
};

// YDAP 日期格式枚举
export const YDAP_DATE_FORMATS = {
  DATE: 'YYYY-MM-DD',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  TIME: 'HH:mm:ss',
  MONTH: 'YYYY-MM',
  YEAR: 'YYYY'
};

// YDAP 设备类型枚举
export const YDAP_DEVICE_TYPES = {
  CAMERA: 'camera',
  SCANNER: 'scanner',
  ID_CARD: 'idCard',
  FINGERPRINT: 'fingerprint'
};

// YDAP 自动完成触发模式枚举
export const YDAP_AUTO_COMPLETE_TRIGGERS = {
  FOCUS: 'focus',
  INPUT: 'input'
};

// YDAP 列表选择模式枚举
export const YDAP_LIST_SELECT_MODES = {
  NONE: 'none',
  SINGLE: 'single',
  MULTIPLE: 'multiple'
};

// YDAP 排序方向枚举
export const YDAP_SORT_DIRECTIONS = {
  ASC: 'asc',
  DESC: 'desc'
};

// YDAP 分页位置枚举
export const YDAP_PAGINATION_POSITIONS = {
  TOP: 'top',
  BOTTOM: 'bottom',
  BOTH: 'both'
};

// YDAP 分页尺寸枚举
export const YDAP_PAGINATION_SIZES = {
  SMALL: 'small',
  DEFAULT: 'default'
};

// YDAP 树形展开模式枚举
export const YDAP_TREE_EXPAND_MODES = {
  ALL: 'all',
  FIRST_LEVEL: 'firstLevel',
  NONE: 'none'
};

// YDAP 提示类型枚举
export const YDAP_TIP_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error'
};

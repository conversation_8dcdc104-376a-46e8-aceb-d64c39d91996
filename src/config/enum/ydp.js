// YDP标签类型枚举
export const YDP_TAGS = {
  // 根层级标签
  PAGE: 'page',
  PARAS: 'paras',
  PARA: 'para',
  DATA_SOURCE: 'data_source',
  SQL: 'sql',
  IFRAMES: 'iframes',
  FORM: 'form',
  SCRIPT: 'script',

  // 表格相关标签
  TABLE: 'table',
  TH: 'th',
  TR: 'tr',
  TD: 'td',
  TD_CHILD: 'td-child',

  // 样式和内容标签
  STYLE: 'style',
  VALUE: 'value',

  // 输入控件标签
  INPUT: 'input',
  OPTION: 'option',
  OPTION_SINGLE: 'option_single',
  MAP: 'map',
  EVENT: 'event',
  FOREGROUNDS: 'foregrounds',
  BACKGROUNDS: 'backgrounds',

  // 扩展行相关标签
  EXT_LINETAG: 'EXT_LINETAG',
  EXT_fdListDic: 'EXT_fdListDic',
  EXT_strSql: 'EXT_strSql',
  ROW: 'row'
};

// 输入控件类型枚举
export const INPUT_TYPES = {
  // 基础文本类控件
  TEXT: 'text',
  HIDDEN: 'hidden',
  PASSWORD: 'password',
  TEXTAREA: 'textarea',

  // 选择类控件
  SELECT: 'select',
  RADIO: 'radio',
  CHECKBOX: 'checkbox',

  // 特殊控件类型
  BUTTON: 'button',
  DATE: 'date',
  AREA: 'area',
  MLD: 'mld',
  IMAGE: 'image',
  NUMBER: 'number',
  GRAPH: 'graph',
  TREE: 'tree',
  EDOC: 'edoc'
};

// 按钮类型枚举
export const BUTTON_TYPES = {
  SUBMIT: 'submit',
  RETURN: 'return',
  RESET: 'reset',
  EXCEL: 'excel',
  PDF: 'pdf',
  WORD: 'word'
};

// 数据源类型枚举
export const DICTYPE = {
  MULTI_LEVEL: 'D',  // 多级字典
  CUSTOM: 'E',       // 自定义
  STATIC: 'T'        // 静态
};

// 验证类型枚举
export const VALIDATION_TYPES = {
  STRING: 'string',
  INT: 'int',
  MONEY: 'money',
  DATE: 'date'
};

// 事件类型枚举
export const EVENT_TYPES = {
  ON_CLICK: 'onClick',
  ON_BLUR: 'onBlur',
  ON_CHANGE: 'onChange',
  ON_FOCUS: 'onFocus',
  ON_LOAD: 'onLoad',
  ON_SUBMIT: 'onSubmit'
};

// 页面属性枚举
export const PAGE_ATTRIBUTES = {
  ROW_COUNT: 'row-count',
  COLUMN_COUNT: 'column-count'
};

// 表单属性枚举
export const FORM_ATTRIBUTES = {
  NAME: 'name',
  ACTION: 'action',
  TITLE: 'title',
  TARGET: 'target',
  CLASS: 'class'
};

// 表格行属性枚举
export const TR_ATTRIBUTES = {
  HEIGHT: 'height',
  IS_PAGE: 'ispage',
  DYNAMIC_TAG: 'dynamictag',
  EXT_IS_ENABLE: 'EXT_IsEnable',
  ROW: 'row',
  CLASS: 'class',
  CURRENT_PAGE_NO: 'currentpageno'
};

// 表格单元格属性枚举
export const TD_ATTRIBUTES = {
  SPAN: 'span',
  TYPE: 'type',
  HIDDEN_EXP: 'hiddenExp',
  NAME: 'name',
  ROWSPAN: 'rowspan',
  COLSPAN: 'colspan',
  COL: 'col',
  ALIGN: 'align'
};

// 输入控件通用属性枚举
export const INPUT_ATTRIBUTES = {
  NAME: 'name',
  VALUE: 'value',
  TYPE: 'type',
  MAXLENGTH: 'maxlength',
  SIZE: 'size',
  READ_ONLY_EXP: 'readOnlyExp',
  HIDDEN_EXP: 'hiddenExp',
  SIGN: 'sign',
  REQUIRED: '_required',
  VALIDATION_TYPE: '_type',
  DESC: '_desc',
  MAX_LENGTH: '_maxlength',
  MIN_LENGTH: '_minlength',
  MAX_VALUE: '_maxvalue',
  MIN_VALUE: '_minvalue',
  PWD2: '_pwd2',
  SAME_VALUE_FIELD: '_samevaluefield',
  ALLOW_NEGATIVE: 'allowNegative',
  DEL_NUM: '_delNum'
};

// 文本域特有属性枚举
export const TEXTAREA_ATTRIBUTES = {
  ROWS: 'rows',
  COLS: 'cols'
};

// 选择控件特有属性枚举
export const SELECT_ATTRIBUTES = {
  DICTYPE: 'dictype',
  SQL: 'sql',
  DATASOURCE_NAME: 'datasourcename',
  VIEW_NAME: 'viewname',
  LEVEL_NUM: 'levelnum',
  SORT_TYPE: 'sorttype',
  BEGIN_VALUE: 'beginvalue',
  IS_SUPER_COMBO: 'isSuperCombo'
};

// 按钮特有属性枚举
export const BUTTON_ATTRIBUTES = {
  BUTTON_TYPE: '_type',
  IS_FRAME: 'isframe',
  IFRAME_NUM: 'iframenum',
  IFRAME_IS_RET: 'iframeisret',
  IFRAME_RET_MES: 'iframeretmes'
};

// 日期控件特有属性枚举
export const DATE_ATTRIBUTES = {
  INIT_VAL: 'initVal',
  IS_VIEW: 'isView'
};

// 地区选择器特有属性枚举
export const AREA_ATTRIBUTES = {
  TYPE_ID: 'typeID',
  LEVEL: 'level'
};

// 多级字典特有属性枚举
export const MLD_ATTRIBUTES = {
  TYPE_ID: 'typeID',
  LEVEL: 'level'
};

// 数字选择器特有属性枚举
export const NUMBER_ATTRIBUTES = {
  START_VALUE: 'startvalue',
  END_VALUE: 'endvalue',
  STEP_VALUE: 'stepvalue'
};

// 树形结构特有属性枚举
export const TREE_ATTRIBUTES = {
  SQL: 'sql',
  PR_VALUE: 'prValue',
  DESC: 'desc',
  IS_WHOLE_PATH: 'isWholePath'
};

// 事件属性枚举
export const EVENT_ATTRIBUTES = {
  EVENT_TYPE: 'eventType',
  EVENT_SCRIPT: 'eventScript'
};

// 数据源属性枚举
export const DATA_SOURCE_ATTRIBUTES = {
  KEY: 'key',
  IS_EXT: 'isext',
  EXT_VALUE: 'extvalue',
  MAX_ROWS: 'maxrows',
  SCOPE_ALL: 'scope_all',
  DATASOURCE_NAME: 'datasourcename'
};

// 选项属性枚举
export const OPTION_ATTRIBUTES = {
  LABEL: 'label',
  VALUE: 'value'
};

// 单级字典选项属性枚举
export const OPTION_SINGLE_ATTRIBUTES = {
  TYPE_ID: 'typeid'
};

// 扩展行属性枚举
export const EXT_LINETAG_ATTRIBUTES = {
  EXT_NAME: 'extname',
  DATA_SOURCE: 'data_source'
};

// 扩展行字段配置属性枚举
export const EXT_FIELD_ATTRIBUTES = {
  EXT_FDNAME: 'EXT_fdname',
  EXT_FDDESC: 'EXT_fddesc',
  EXT_FD_IS_EDIT: 'EXT_fdIsEdit',
  EXT_FD_IS_HIDDEN: 'EXT_fdIsHidden'
};

// 单元格类型枚举
export const TD_TYPES = {
  TEXT: 'text',
  COMMON: 'common',
  LABEL: 'label',
  INPUT: 'input'
};

// 布尔值枚举
export const BOOLEAN_VALUES = {
  TRUE: 'true',
  FALSE: 'false'
};

// 表达式前缀枚举
export const EXPRESSION_PREFIX = {
  EQUALS: '=',
  HASH: '#'
};

// 时间精度类型枚举
export const TIME_TYPES = {
  YEAR: 'year',
  MONTH: 'month',
  DAY: 'day',
  HOUR: 'hour',
  MINUTE: 'minute',
  SECOND: 'second'
};

// 忽略标签及其属性的配置（已处理内容）
export const HANDLED_TAGS = {
  XML: {
    tag: '?xml',
    attributes: ['@_version', '@_encoding']
  },
  PAGE: {
    tag: 'page',
    attributes: ['@_row-count', '@_column-count']
  },
  DATA_SOURCE: {
    tag: 'data_source',
    attributes: []
  },
  SQL: {
    tag: 'sql',
    attributes: ['@_key', '@_isext', '@_extvalue', '@_maxrows', '@_scope_all', '@_datasourcename', '#text']
  },
  IFRAMES: {
    tag: 'iframes',
    attributes: []
  },
  IFRAME: {
    tag: 'iframe',
    attributes: ['@_name', '@_value', '@_triggername', '@_iframeisret', '@_iframeretmes', '@_sql', '@_datasourcename']
  },
  TARGET: {
    tag: 'target',
    attributes: ['@_targetname', '@_targetvalue', '@_targetshow', '@_targettype']
  },
  FORM: {
    tag: 'form',
    attributes: ['@_name', '@_action', '@_title', '@_target', '@_class']
  },
  SCRIPT: {
    tag: 'script',
    attributes: ['@_value']
  },
  TABLE: {
    tag: 'table',
    attributes: ['@_align', '@_height']
  },
  TH: {
    tag: 'th',
    attributes: []
  },
  TR: {
    tag: 'tr',
    attributes: ['@_height', '@_ispage', '@_dynamictag', '@_EXT_IsEnable', '@_row', '@_class', '@_currentpageno']
  },
  TD: {
    tag: 'td',
    attributes: ['@_span', '@_type', '@_hiddenExp', '@_name', '@_rowspan', '@_colspan', '@_col', '@_align', '@_isdisplay', '#text', 'style', 'value']
  },
  TD_CHILD: {
    tag: 'td-child',
    attributes: ['@_row', '@_type', '@_hiddenExp', '@_name', '@_rowspan', '@_colspan', '@_col']
  },
  INPUT: {
    tag: 'input',
    attributes: ['@_name', '@_value', '@_type', '@__type', '@_maxlength', '@_size', '@_readOnlyExp', '@_hiddenExp', '@_sign', '@_required', '@__required', '@_validation_type', '@_desc', '@_maxlength', '@_minlength', '@_maxvalue', '@_minvalue', '@_pwd2', '@_samevaluefield', '@_allowNegative', '@_delNum', '@_readonly', '@_initVal', '@__delNum', '@__maxvalue', '@__minvalue']
  },
  VALUE: {
    tag: 'value',
    attributes: ['#text']
  },
  STYLE: {
    tag: 'style',
    attributes: 'all'
  },
  EXT_LINETAG: {
    tag: 'EXT_LINETAG',
    attributes: 'all'
  },
  EXT_FDLISTDIC: {
    tag: 'EXT_fdListDic',
    attributes: 'all'
  },
  ROW: {
    tag: 'row',
    attributes: ['@_EXT_fdIsHidden', '@_EXT_fdIsEdit', '@_EXT_fddesc', '@_EXT_fdname', '#text']
  },
  EXT_STRSQL: {
    tag: 'EXT_strSql',
    attributes: ['#text']
  }
};

// 逻辑表达式 值范围
export const BOOLEAN_RANGE = {
  ONE: '1',
  ZERO: '0',
  TRUE: 'true',
  FALSE: 'false',
  EXPRESSION: 'expression',
};

'use strict';
import { promises as fs, existsSync } from 'fs';
import { resolve, join } from 'path';
import chalk from 'chalk';
import * as logger from './logger.js';
import getArgs from './get-args.js';
import convertYdp from './convert-ydp.js';

main().then(stats => {
  if (stats.totalCount > 0) {
    logger.info('处理完成，共', stats.totalCount, '个文件，其中', stats.successCount, '个成功', stats.errorCount, '个失败');
    if (stats.totalCount > 1 || stats.errorCount > 0) pause();
  }
}).catch(err => {
  logger.error(err);
  pause();
});

/**
 * 依次处理每个命令行参数
 */
async function main() {
  //从命令行参数获取文件路径
  let args = getArgs();
  // let args = [
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\DaiKuanShenHe\\pages\\120001_1.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\DaiKuanShenHe\\pages\\120001_2.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\DaiKuanShenHe\\pages\\120001_3.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\DaiKuanShenHe\\pages\\120001_4.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\DaiKuanShenHe\\pages\\120001_5.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\ShenZhenYdp\\ydp\\111666\\addendnum.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\ShenZhenYdp\\ydp\\111667\\selaccdatil.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\ShenZhenYdp\\ydp\\120800\\ln120800_1.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\ShenZhenYdp\\ydp\\120800\\ln120800_2.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\ShenZhenYdp\\ydp\\120800\\ln120800_3.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\ShenZhenYdp\\ydp\\ac\\130019\\130019_01.ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-pages\\ShenZhenYdp\\ydp',
  //   // 'C:\\hxydCo\\Projects\\migration\\ydp\\ydp-helper\\samples\\ln120185_03.ydp'
  // ];

  //统计数据（处理文件总数、成功数、失败数）
  let stats = {
    totalCount: 0,
    successCount: 0,
    errorCount: 0,
  };

  if (args) {
    logger.debug('args: ', args);
    for (let arg of args) {
      logger.debug('arg: ', arg);
      let [filePath, isDirectory] = await completePath(arg);
      await parseFiles(filePath, isDirectory, stats);
    }
  }
  return stats;
}

/**
 * 遍历目录文件
 * @param {String} filePath 文件或目录完整路径
 * @param {Boolean} isDirectory 是否目录(未找到则返回undefined)
 * @param {Object} stats 统计数据
 */
async function parseFiles(filePath, isDirectory, stats) {
  logger.debug('filePath, isDirectory: ', filePath, isDirectory);
  if (isDirectory === undefined) {
    logger.error('文件或目录不存在：', filePath);
    stats.errorCount++;
    stats.totalCount++;
    return;
  }
  if (isDirectory) {
    let files = await fs.readdir(filePath, { withFileTypes: true });
    for (let file of files) {
      if (file.isDirectory()) {
        await parseFiles(join(filePath, file.name), true, stats);
      }
      else if (file.name.toLowerCase().endsWith('.ydp')) {
        await convertYdp(join(filePath, file.name), stats);
      }
    }
  }
  else if (filePath.toLowerCase().endsWith('.ydp')) {
    await convertYdp(filePath, stats);
  }
}

/**
 * 补全完整路径
 * @param {String} arg 文件或目录名，路径或扩展名可有可无
 * @returns [完整路径，是否目录]
 */
async function completePath(arg) {
  let filePath = resolve(arg);
  if (existsSync(filePath)) {
    logger.debug('filePath: ', filePath);
    let stat = await fs.stat(filePath);
    return [filePath, stat.isDirectory()];
  }
  else if (!filePath.toLowerCase().endsWith('.ydp')) {
    return completePath(filePath + '.ydp');
  }
  else {
    return [arg];
  }
}

function pause() {
  console.info(chalk.gray('\n请按回车键继续...'));
  process.stdin.read();
}
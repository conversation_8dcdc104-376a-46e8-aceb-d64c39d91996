import chalk from 'chalk';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packageJson = JSON.parse(readFileSync(join(__dirname, '../package.json'), 'utf8'));
const { name, version, description } = packageJson;

export default () => {

  //输出应用标题
  console.info(`\n${chalk.red(`【${description}】`)}\n${chalk.yellow(name)} ${chalk.green(`v${version}`)}\n`);

  //获取命令行参数
  let args = process.argv.slice(2);

  //如果没有提供命令行参数，显示用法
  if (!args.length) {
    console.info(`${chalk.cyan('用法：')}
  ${chalk.yellow('ydp-helper')} ${chalk.green('[path][filename][.ydp] ...')}

${chalk.cyan('示例：')}
  ${chalk.gray('1. 提供要转换的 ydp 文件名，默认文件在当前目录下')}
  ${chalk.yellow('ydp-helper')} aaa.ydp
  ${chalk.gray('2. 可以省略文件扩展名')}
  ${chalk.yellow('ydp-helper')} aaa
  ${chalk.gray('3. 可以使用相对路径')}
  ${chalk.yellow('ydp-helper')} ..\\yyy\\aaa.ydp
  ${chalk.gray('4. 可以使用绝对路径')}
  ${chalk.yellow('ydp-helper')} d:\\xxx\\yyy\\aaa.ydp
  ${chalk.gray('5. 如果路径或文件名中包含空格，要加引号')}
  ${chalk.yellow('ydp-helper')} "d:\\my work\\aaa.ydp"
  ${chalk.gray('6. 可以一次提供多个文件，用空格分开')}
  ${chalk.yellow('ydp-helper')} aaa bbb "hello world"
  ${chalk.gray('7. 如果提供的是目录名，将批量转换目录下所有 ydp 文件')}
  ${chalk.yellow('ydp-helper')} d:\\xxx\\yyy
  ${chalk.gray('8. 一条命令中可同时包含文件和目录，可同时使用绝对路径或相对路径')}
  ${chalk.yellow('ydp-helper')} aaa.ydp bbb "yyy\\hello world" d:\\xxx\\ddd

${chalk.cyan('更简单的用法：')}
  直接把 ydp 文件或目录拖放到 ${chalk.yellow('ydp-helper.exe')} 文件上面就行了，可以一次拖放多个文件或目录。

${chalk.gray('请按回车键继续...')}`);
    process.stdin.read();
  }
  else {
    return args;
  }

};
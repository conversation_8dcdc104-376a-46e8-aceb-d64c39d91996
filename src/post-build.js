import { renameSync } from 'fs';
import { resolve } from 'path';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packageJson = JSON.parse(readFileSync(join(__dirname, '../package.json'), 'utf8'));
const { name, version } = packageJson;

try {
  renameSync(resolve('build/ydp-helper-win-x86.exe'), resolve(`build/bin/win/ydp-helper-${version}.exe`));
  renameSync(resolve('build/ydp-helper-linux-x64'), resolve(`build/bin/linux/ydp-helper-${version}`));
  renameSync(resolve('build/ydp-helper-macos-x64'), resolve(`build/bin/macos/ydp-helper-${version}`));
}
catch (err) {
  console.log('移动文件出错', err);
}

console.log(`
  <li>${name}（v${version}） 
      <a href="tools/ydp-helper/win/ydp-helper-${version}.exe?v=${version}">下载Windows版</a>
      <a href="tools/ydp-helper/macos/ydp-helper-${version}?v=${version}">下载MacOS版</a>
      <a href="tools/ydp-helper/linux/ydp-helper-${version}?v=${version}">下载Linux版</a>
      <a target="_blank" href="tools/ydp-helper/manual.mp4?v=${version}">使用方法视频</a>
  </li>
`);
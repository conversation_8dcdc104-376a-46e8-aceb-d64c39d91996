import { HANDLED_TAGS } from '../config/enum/index.js';
import ActionManager from "../parse/action/ActionManager.js";

/**
 * 分析 YDP 文件并提取未成功转换的内容。
 * @class Analyzer
 */
class Analyzer {
  /**
   * 创建 Analyzer 实例。
   * @param {Object} ydpJson - YDP文件转换后的JSON对象。
   */
  constructor(ydpJson) {
    this.ydpJson = ydpJson;
    this.unconvertedTagAttrs = [];
    this.unhandledFormEvents = [];
    this.javascript = {};
    this.dmlEventActions = [];
    this.recordedTags = new Set(); // 用于记录已记录的标签
  }

  /**
   * 分析整个 YDP JSON 对象。
   * @returns {Object} - 包含所有分析结果的对象。
   */
  async analyze() {
    await this.analyzeYDPJson();
    await this.analyzeJavaScript();
    await this.analyzeDMLActions();

    return {
      unconvertedTagAttrs: this.unconvertedTagAttrs,
      unhandledFormEvents: this.unhandledFormEvents,
      javascript: this.javascript,
      dmlEventActions: this.dmlEventActions
    };
  }

  /**
   * 分析 YDP JSON 对象中的标签、属性和表单事件。
   */
  async analyzeYDPJson() {
    const tagsToIgnore = new Set(Object.values(HANDLED_TAGS).map(tag => tag.tag));

    /**
     * 检查标签是否被处理。
     * @param {String} tag - 标签名。
     * @returns {Boolean} - 标签是否被处理。
     */
    const isHandledTag = (tag) => {
      return tagsToIgnore.has(tag);
    };

    /**
     * 获取标签被处理的属性。
     * @param {String} tag - 标签名。
     * @returns {Set} - 属性集合。
     */
    const getHandledAttributes = (tag) => {
      const tagConfig = Object.values(HANDLED_TAGS).find(config => config.tag === tag);
      if (tagConfig && tagConfig.attributes === 'all') {
        return new Set(['all']);
      }
      return new Set(tagConfig ? tagConfig.attributes : []);
    };

    /**
     * 从路径中获取标签和父标签。
     * @param {Array} path - 当前路径。
     * @returns {Object} - 包含标签和父标签的对象。
     */
    const getTags = (path) => {
      if (path.length === 0) {
        return { parentTag: '', tag: '' };
      }

      let tag = path[path.length - 1];
      let parentTag = '';

      // 如果 tag 是数字，则往前找第一个不是数字的值作为 tag
      if (!isNaN(Number(tag))) {
        for (let i = path.length - 2; i >= 0; i--) {
          if (isNaN(Number(path[i]))) {
            tag = path[i];
            break;
          }
        }
      }

      // 获取 parentTag：tag 的前一位，且不是数字
      if (path.length >= 2) {
        const parentIndex = path.indexOf(tag) - 1;
        if (parentIndex >= 0 && isNaN(Number(path[parentIndex]))) {
          parentTag = path[parentIndex];
        }
      }

      return { parentTag, tag };
    };

    /**
     * 记录未转换的标签或属性。
     * @param {String} type - 类型（tag 或 attribute）。
     * @param {String} tag - 标签名或属性名。
     * @param {Array} position - 位置路径。
     * @param {String} reason - 原因。
     * @param {String} remark - 备注。
     */
    const recordUnconverted = (type, tag, position, reason, remark) => {
      this.unconvertedTagAttrs.push({
        type,
        tag,
        position: position.join('.'),
        reason,
        remark,
      });
    };

    /**
     * 记录未处理的表单事件。
     * @param {String} type - 事件类型。
     * @param {Array} position - 事件位置。
     * @param {String} fieldName - 字段名。
     * @param {String} eventType - 事件类型。
     * @param {String} eventAction - 事件动作。
     * @param {String} remark - 备注。
     */
    const recordUnhandledFormEvent = (type, position, fieldName, eventType, eventAction, remark) => {
      this.unhandledFormEvents.push({
        type,
        position: position.join('.'),
        fieldName,
        eventType,
        eventAction,
        remark,
      });
    };

    /**
     * 递归遍历 JSON 对象。
     * @param {Object} json - 当前 JSON 对象。
     * @param {Array} path - 当前路径。
     * @param {Object} parentJson - 父 JSON 对象。
     */
    const traverse = (json, path = [], parentJson) => {
      for (const key in json) {
        if (json.hasOwnProperty(key)) {
          const value = json[key];
          const newPath = [...path, key];

          if (typeof value === 'object' && value !== null) {
            if (Array.isArray(value)) {
              value.forEach((item, index) => {
                traverse(item, [...newPath, index], json);
              });
            }
            else {
              traverse(value, newPath, json);
            }
          }
          else {
            // 获取当前正在分析的标签元素
            const { tag, parentTag } = getTags(path);

            if (!isHandledTag(tag)) {
              // 记录未处理的表单事件
              if (tag === 'event') {
                const { '@_eventType': eventType, '@_eventScript': eventAction } = json;
                let fieldName = '';

                // 提取 parentJson 中的信息
                if (parentJson['@_id']) {
                  fieldName = `${parentTag}[id=${parentJson['@_id']}]`;
                }
                else if (parentJson['@_name']) {
                  fieldName = `${parentTag}[name=${parentJson['@_name']}]`;
                }
                else {
                  fieldName = `${parentTag}`;
                }
                if (eventType && eventAction) {
                  recordUnhandledFormEvent.call(this, '事件', newPath, fieldName, eventType, eventAction, `建议在YDAP表单项中配置 ${eventType} 事件`);
                }
                return;
              }
              // 记录未处理的标签
              else if (!this.recordedTags.has(tag)) {
                recordUnconverted.call(this, '标签', tag, newPath, '未处理的标签', '');
                this.recordedTags.add(tag);
              }
            }
            else {
              const handledAttributes = getHandledAttributes(tag);
              if (handledAttributes.has('all')) {
                continue; // 忽略该标签所有属性
              }
              // 记录未处理的属性
              if (!handledAttributes.has(key)) {
                recordUnconverted.call(this, '属性', tag, newPath, '未处理的属性' + key, '');
              }
            }
          }
        }
      }
    }

    traverse(this.ydpJson);
  }

  /**
   * 分析 JavaScript 脚本。
   */
  async analyzeJavaScript() {
    if (this.ydpJson.page && this.ydpJson.page.form) {
      const form = this.ydpJson.page.form;
      if (form.script) {
        const scriptContent = form.script['@_value'];
        this.javascript = {
          source: scriptContent,
          analysis: `脚本内容：${scriptContent}`,
          conversion: `建议将此脚本迁移到YDAP对应的事件处理器中`
        };
      }
    }
  }

  /**
   * 分析 DML 操作，收集大模型生成的事件动作配置
   */
  async analyzeDMLActions() {
    const actionManager = ActionManager.getInstance();
    const dmlActions = actionManager.getAllActions();

    // 将 Map 转换为普通对象
    const mapToObject = Object.fromEntries(dmlActions);

    // 统计 DML 操作并整理成对象数组
    const dmlEventActions = Object.values(mapToObject).map(action => {
      const { id, isExecuteOnLoad, sqlInfo, action: actionInfo, sourceName, createdAt } = action;

      return {
        id,
        isExecuteOnLoad, // 操作是否在加载时执行
        dmlOperationType: sqlInfo.dml_operation_type, // DML 操作的类型，例如 INSERT、UPDATE、DELETE 等
        selectColumns: sqlInfo.select_columns, // 在查询中选择的列
        targetSourceData: sqlInfo.target_source_data, // 目标数据源
        conditionsFilters: sqlInfo.conditions_filters, // 查询条件和过滤器
        // groupOrderBy: sqlInfo.group_order_by, // 分组和排序信息
        joins: sqlInfo.joins, // 连接信息
        aggregationsFunctions: sqlInfo.aggregations_functions, // 聚合函数
        otherAuxiliary: sqlInfo.other_auxiliary, // 其他辅助信息
        // dynamicContent: sqlInfo.dynamicContent, // 动态内容
        action: actionInfo, // 操作信息
        sourceName, // 数据源名称
        createdAt, // 创建时间
      };
    });

    this.dmlEventActions = dmlEventActions;
  }
}

export default Analyzer;
'use strict';

/**
 * 解析 xml 元素内容
 * @param {Object[]} elements 元素对象数组
 * @param {Object} parent 上层父元素对象
 * @param {Object} r 保存解析结果的对象
 */
async function parseElements(elements, parent, r) {
  for (let i = 0; i < elements.length; i++) {
    let ele = elements[i];

    //忽略td-child元素
    if (ele.name === 'td-child') continue;
    //从表单元素属性中获取页面标题
    if (ele.name === 'form') {
      r.title = ele.attributes.title;
    }
    //表单输入字段
    if (ele.name === 'input') {
      // 原 ydp 表单元素属性
      let {
        name, type, _type, value = '', initVal = '', beginvalue = '',
        _required, readOnlyExp, hiddenExp,
        maxlength, _maxlength, _minlength, colspan,
        _delNum, _minvalue, _maxvalue,
        viewname, sql, datasourcename
      } = ele.attributes;

      // 必输条件，只读条件，隐藏条件属性转换
      if (_required && (_required.trim() === '1==1' || _required.trim() === '1')) _required = 'true';
      if (readOnlyExp) {
        readOnlyExp = readOnlyExp.replace(/^rw/, '%c.rw');
        if (readOnlyExp.trim() === '1==1' || readOnlyExp.trim() === '1') readOnlyExp = 'true'
      }
      if (hiddenExp && (hiddenExp.trim() === '1==1' || hiddenExp.trim() === '1')) hiddenExp = 'true';

      // 表单字段定义
      let field = { id: name, fieldType: 'Input' };

      // 表单校验规则
      let rules = [];
      // 必填校验
      if (_required && _required !== 'false') rules.push({ name: 'required', value: 'true' });
      // 最大长度校验
      if (maxlength || _maxlength) rules.push({ name: 'maxLen', value: (maxlength || _maxlength) });
      // 最小长度校验
      if (_minlength) rules.push({ name: 'minLen', value: _minlength });

      // 普通输入框
      if (type === 'text') {
        // 正整数
        if (_type === 'int') {
          field = { ...field, fieldType: 'Number' };
        }
        // 数字
        else if (_type === 'number') {
          rules.push({ name: 'type', value: 'number' });
        }
        // 金额
        else if (_type === 'money') {
          field = {
            ...field, fieldType: 'Money',
            ...(_delNum ? { precision: _delNum } : {}),
            ...(_minvalue ? { min: _minvalue } : {}),
            ...(_maxvalue ? { max: _maxvalue } : {}),
            // 金额输入框后加元，其他暂时不管
            suffix: '元',
          };
        }
        // 日期
        else if (_type === 'date') {
          field = { ...field, fieldType: 'DateTime' };
        }
      }
      // 下拉框
      else if (type === 'select') {
        field = { ...field, fieldType: 'Select', options: 'test' };
      }
      // 单选框
      else if (type === 'radio') {
        field = { ...field, fieldType: 'Radio', options: 'test' };
      }
      // 复选框
      else if (type === 'checkbox') {
        field = { ...field, fieldType: 'Checkbox', options: 'test' };
      }
      // 多行文本输入框
      else if (type === 'textarea') {
        field = { ...field, fieldType: 'TextArea' };
      }
      // 密码输入框
      else if (type === 'password') {
        field = { ...field, fieldType: 'Password' };
      }
      // 数字
      else if (type === 'number') {
        field = { ...field, fieldType: 'Number' };
      }
      // 日期输入框
      else if (type === 'date') {
        field = { ...field, fieldType: 'DateTime' };
      }
      // 按钮
      else if (type === 'button') {
        field = { ...field, fieldType: 'Button' };
      }

      field = {
        ...field,
        sn: r.inputs.length + 1,
        blockType: 'Form', //内容块类型
        label: r.lastLabel || (hiddenExp === 'true' ? '隐藏框_' + name : ''), //字段标签, 给隐藏输入框添加标签，不考虑表达式非1==1的情况
        name, //字段名称
        value: deq(value || initVal || beginvalue), //字段默认值
        origType: type, //原控件类型
        dataType: _type, //原数据类型
        ...(_required ? { required: _required } : {}), //必输条件
        ...(readOnlyExp ? { readonly: readOnlyExp } : {}), //只读条件
        ...(hiddenExp ? { hidden: hiddenExp } : {}), //隐藏条件
        ...(maxlength ? { maxlength } : {}),
        ...(rules.length > 0 ? { rules } : {}), //表单校验规
        ...(viewname ? { viewname } : {}),
        ...(datasourcename ? { datasourcename } : {}), //选项数据源
      };

      // 自定义响应事件和下拉框
      let events = [], options = [];
      if (ele.elements && ele.elements.length > 0) {
        ele.elements.forEach(el => {
          if (el.name === 'event') {
            // 自定义响应事件和自定义Script
            let { eventType, eventScript } = el.attributes;
            events.push({ eventType, eventScript });
          }
          else if (el.name === 'option') {
            // 下拉框原的显示值和原数据值
            let { label, value } = el.attributes;
            options.push({ label, value });
          }
        });

        //原调用方式，原调用函数
        if (events.length > 0) {
          field.eventTypes = events.map(event => event.eventType).join(',\n');
          field.eventScripts = events.map(event => event.eventScript).join(',\n');
        }

        //原显示值，原数据值
        if (options.length > 0) {
          field.optionLabels = options.map(option => deq(option.label)).join(',\n');
          field.optionValues = options.map(option => deq(option.value.trim()) || '空').join(',\n');
        }
      }
      //选项代码，对应sql中以typeid开头的变量名
      if (sql) {
        field.sql = deq(sql);
        field.options = getOptions(deq(sql));
        field.optionsStr = field.options.join(',');
      }
      r.inputs.push(field);
      r.lastLabel = '';
    }
    //隐式提交
    else if (ele.name === 'iframe') {
      let { value, triggername, iframeisret, iframeretmes, sql, datasourcename } = ele.attributes;
      iframeisret = iframeisret === 'true' ? '是' : '否';
      // 获取查询条件变量及查询条件变量类型
      let { sqlRepl, quecondvar, quecondvartype } = getQueCondVar(deq(sql));
      let inputDatas = getInputDatas(deq(sql)); //查询条件变量
      let outDatas = []; //输出变量

      if (ele.elements && ele.elements.length) {
        ele.elements.forEach(target => {
          let { targetname, targetvalue } = target.attributes;
          targetvalue = deq(targetvalue);
          // logger.debug('target, index: ', target, index);
          outDatas.push({
            fieldName: targetname, //页面字段名
            eventFieldName: targetvalue, //子事件字段名
            eventFieldType: "输出", //子事件字段类型
          });
        });
      }
      // 整合后的输入、输出变量
      let datas = [...inputDatas, ...outDatas];
      datas.forEach((data, index) => {
        r.ajaxs.push({
          ...(index === 0 ? {
            typeName: '隐式提交', //子事件类型
            iframeisret, //无数据是否报错
            iframeretmes, //报错信息
            datasourcename, //数据源
            sql: sqlRepl, //子事件处理语句
            quecondvar, //查询条件变量
            quecondvartype, //查询条件变量类型
            eventid: value, //原事件编号
            triggername, //原触发字段名
          } : {}),
          fieldName: data.fieldName, //页面字段名
          eventFieldName: data.eventFieldName, //子事件字段名
          eventFieldType: data.eventFieldType, //子事件字段类型
        });
      });
    }
    //数据源
    else if (ele.name === 'data_source') {
      if (ele.elements && ele.elements.length > 0) {
        ele.elements.forEach((datasource, index) => {
          let { key, datasourcename } = datasource.attributes;
          let sqlTxt = deq(datasource.elements[0].text); //sql查询语句 
          let inputDatas = getInputDatas(sqlTxt); //查询条件变量

          if (inputDatas.length > 0) {
            inputDatas.forEach((inputData, index) => {
              r.datasets.push({
                ...(index === 0 ? {
                  typeName: '数据集', //子事件类型
                  iframeisret: '否', //无数据是否报错
                  datasourcename, //数据源
                  sql: sqlTxt, //子事件处理语句
                  eventid: key, //原事件编号
                } : {}),
                fieldName: inputData.fieldName, //页面字段名
                eventFieldName: inputData.eventFieldName, //子事件字段名
                eventFieldType: '输入', //子事件字段类型
              });
            });
          }
          else {
            r.datasets.push({
              typeName: '数据集', //子事件类型
              iframeisret: '否', //无数据是否报错
              datasourcename, //数据源
              sql: sqlTxt, //子事件处理语句
              eventid: key, //原事件编号
            });
          }
        });
      }
    }
    //文本标签
    else if (ele.name === 'value' && ele.elements && ele.elements[0].text) {
      // 设置页面标题
      if (!r.title && !r.lastLabel) {
        r.title = ele.elements[0].text;
        return
      }

      // 忽略当前元素所在Tr行
      if (parent.parent && parent.parent.ignore) return;

      if (r.lastLabel) {
        // 参考=ds2.sumloan[1]和=getOrgName(selaccdatil_ds3.accinstcode1[1])格式，将内容调整为普通文本显示
        let re = /^=(?:\w|\()+\.(\w+)(?:\[\d\]\)?)?$/;
        if (re.test(ele.elements[0].text)) {
          ele.elements[0].text.replace(re, (match, p1) => {
            r.inputs.push({
              sn: r.inputs.length + 1,
              fieldType: 'Text', //文本显示
              blockType: 'Form', //内容块类型
              label: r.lastLabel, //字段标签
              value: p1, //字段默认值
            });
          });
          r.lastLabel = '';
          return;
        }

        r.inputs.push({
          sn: r.inputs.length + 1,
          // fieldType: 'Text', //文本显示
          // blockType: 'Form', //内容块类型
          label: '文本显示', //字段标签
          value: r.lastLabel, //字段默认值
        });
      }
      r.lastLabel = dcolon(deq(ele.elements[0].text));

      if (r.lastLabel) {
        // 数据集的输出类型变量
        if (r.datasets.length > 0) {
          //数据集ID对应的默认值的正则匹配模式
          let re = /(\w+)\.(\w+)(?:\[#\])?/g;
          let tempArr = [];

          while ((tempArr = re.exec(r.lastLabel)) !== null) {
            r.datasets.forEach((dataset, index) => {
              if (dataset.eventid === tempArr[1]) {
                if (!dataset.fieldName) {
                  r.datasets[index].fieldName = tempArr[2];
                  r.datasets[index].eventFieldName = tempArr[2];
                  r.datasets[index].eventFieldType = '输出';
                }
                else {
                  r.datasets.splice(index + 1, 0, {
                    fieldName: tempArr[2],
                    eventFieldName: tempArr[2],
                    eventFieldType: '输出'
                  });
                }
              }
            });
          }
        }

        // 静态列表数据
        if (r.lastLabel.includes('[#]')) {
          let listId = '';
          r.lastLabel.replace(/(.+\()?(.+)\..+\[\#\]\)?/, (match, p1, p2) => listId = p2);
          // 列表是否重复添加
          if (r.lists.length > 0 && listId && r.lists.map(list => list.id).includes(listId)) continue;

          let list = new Object();
          list.id = listId;
          list.columns = new Array(); //列表列定义
          list.blockType = 'List'; //内容块类型
          list.label = '列表_' + listId; //列表 id 
          list.name = listId; //字段名称
          let trEl = parent.parent; //当前行Tr元素
          let preTrEl = parent.parent.parent.elements[parent.parent.index - 1]; //上一行Tr元素
          let isList = true;

          if (trEl && trEl.name === 'tr' && trEl.elements.length > 0) {
            trEl.elements.forEach((tdEl, index) => {
              if (tdEl.elements[1].elements && tdEl.elements[1].elements[0]) {
                let valEl = tdEl.elements[1].elements[0]; //对应value元素，存放列表ID数据
                let re = /.+\.(\w+)(?:\[\#\])?/; // 获取列表列ID
                let preTdEl = preTrEl.elements[index]; //列标题行Td
                let label = preTdEl && preTdEl.elements  //列标题行td子元素
                  && preTdEl.elements[1]
                  && preTdEl.elements[1].elements
                  && preTdEl.elements[1].elements[0]
                  && preTdEl.elements[1].elements[0].text || '';
                //满足列表列ID和列标题都存在
                if (valEl.text && label) {
                  if (re.test(valEl.text)) {
                    valEl.text.replace(re, (match, p1) => {
                      list.columns.push({ label, id: p1 });
                    });
                  }
                  else list.columns.push({ label, id: deq(valEl.text) });
                  // 将该Tr行元素设置忽略  
                  if (!trEl.ignore) trEl.ignore = true;
                  // 删除已经添加的Text组件
                  r.inputs = r.inputs.filter(item => item.value !== label);
                }
                else {
                  isList = false;
                }
              }
            });
            if (isList) {
              r.lists.push(list);
              r.lastLabel = '';
            }
          }
        }
      }
    }
    //js脚本
    else if (ele.name === 'script') {
      r.javascript = (ele.attributes && ele.attributes.value || '')
        //替换回车换行
        .replace(/\$enter\$/g, '\r\n')
        //清理空白行
        .replace(/(\r\n\s*\r\n)+/g, '\r\n');

      r.messages = parseMessage(r.javascript);
    }
    //动态扩展行
    else if (ele.name === 'EXT_LINETAG') {
      let { extname } = ele.attributes;
      let dynExtRow = { typeName: '动态扩展行', blockType: 'List', id: extname, eventid: extname, iframeisret: '否' };
      let columns = [], fields = [];

      if (ele.elements.length) {
        //解析 EXT_strSql 标签元素
        if (ele.elements[1].name === 'EXT_strSql' && ele.elements[1].elements.length > 0) {
          let sql = ele.elements[1].elements[0].text;
          let inputDatas = [];
          if (sql) {
            sql = deq(sql);
            inputDatas = getInputDatas(sql); //查询条件变量
            fields = [...fields, ...inputDatas];
          }
        }
        //解析 EXT_fdListDic 标签元素
        if (ele.elements[0].name === 'EXT_fdListDic'
          && ele.elements[0].elements
          && ele.elements[0].elements.length > 0) {
          ele.elements[0].elements.forEach(row => {
            let { EXT_fdname, EXT_fddesc } = row.attributes;
            columns.push({ id: EXT_fdname, label: EXT_fddesc });
            fields.push({
              label: EXT_fddesc,
              name: EXT_fdname,
              fieldName: EXT_fdname,
              eventFieldName: EXT_fdname,
              eventFieldType: '输出',
            });
          });
        }
        if (columns.length > 0) {
          dynExtRow.columns = columns;
          fields.forEach((field, index) => {
            if (index === 0) {
              r.dynExtRows.push({ ...dynExtRow, ...field });
            }
            else {
              r.dynExtRows.push(field);
            }
          });

        }
      }
    }
    //递归处理子元素
    else if (ele.elements) {
      ele.parent = parent; // 父元素
      ele.index = i; // 父元素下标
      parseElements(ele.elements, ele, r);
    }
  }
}

/**
 * 删除表达式前置等号
 * @param {String} str 形如=xxx的字符串表达式
 * @returns {String} xxx
 */
function deq(str) {
  return str.replace(/^=/, '');
}

/**
 * 删除字段标签尾部冒号
 * @param {String} str 字段标签
 * @returns {String} 删除了尾部冒号
 */
function dcolon(str) {
  return str.replace(/\s*[:：]\s*$/, '');
}

/**
 * 根据sql查询条件获取对应的选项代码
 * @param {String} sql sql查询条件
 * @returns {String} 选项代码
 */
function getOptions(sql) {
  // 选项代码
  let options = [];
  // 选项代码匹配的正则表达式模式
  let re = /typeid\s*=\s*'([A-Za-z_]\w*)'/gi;
  let tempArr = [];

  while ((tempArr = re.exec(sql)) !== null) {
    options.push(tempArr[1]);
  }
  return options;
}

/**
 * 从隐式提交的sql查询条件中提取查询条件变量及查询条件变量类型，并用？替换对应的变量名
 * @param {String} sql sql查询条件
 * @returns {Object} 包括查询条件变量、查询条件变量类型
 */
function getQueCondVar(sql) {
  // 正则表达式模式，需要考虑sql模糊查询的情况，eg. where projectname like '%"+projectname+"%'
  let re = /('?%?)"\s*\+\s*([A-Za-z_]\w*)\s*\+\s*"(%?'?)/g;
  // 查询条件变量，查询条件变量类型
  let quecondvar = [], quecondvartype = [];

  let sqlRepl = sql.replace(re, (match, p1, p2, p3, offset, string) => {
    quecondvar.push(p2)
    if (p1 === "'%") {
      quecondvartype.push('String');
      return "'%?%'";
    }
    else {
      if (p1 && p1.length > 0)
        quecondvartype.push('String');
      else
        quecondvartype.push('Number');

      return "?";
    }
  });

  return {
    sqlRepl,
    quecondvar: quecondvar.join(','),
    quecondvartype: quecondvartype.join(',')
  }
}

/**
 * 解析javascript中的sendHTMLMessage函数，并提取对应参数
 * @param {String} str ydp中的js代码
 * @returns {Object[]}
 */
function parseMessage(str) {
  // 选项代码匹配的正则表达式模式
  let re = /sendHTMLMessage\("\d+","(\d+)",.+\);?/g;
  let tempArr = [], messages = [];
  // sendHTMLMessage("881","53","<%=_contexPath%>");
  while ((tempArr = re.exec(str)) !== null) {
    messages.push({ typenNme: '隐式发报文', iframeisret: '否', sql: tempArr[1], value: tempArr[1] });
  }
  return messages;
}

/**
 * 从sql查询条件中提取除以下划线开头全大写字母拼接的查询条件变量
 * @param {String} sql sql查询条件
 * @returns {Object[]}
 */
function getInputDatas(sql) {
  // 正则表达式模式，需要考虑sql模糊查询的情况，eg. where projectname like '%"+projectname+"%'
  let re = /'?%?"\s*\+\s*([A-Za-z]\w*|_[a-z0-9]\w*|_\w*[a-z0-9_]|)\s*\+\s*"%?'?/g;
  let inputDatas = [], tempArr;

  while ((tempArr = re.exec(sql)) !== null) {
    // console.log('tempArr: ', tempArr);
    inputDatas.push({ fieldName: tempArr[1], eventFieldName: tempArr[1], eventFieldType: "输入" })
  }
  return inputDatas;
}

export default parseElements;
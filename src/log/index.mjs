import path from 'path';
import fs from 'fs';
import dayjs from 'dayjs';
import { getDirPath, writeFile } from '../files/index.mjs';
import { LOG_TYPE } from '../config/enum/index.js';

const getLogPath = () => {
  const logPath = path.join(getDirPath(), '../../logs');
  return process.env.LOG_PATH || logPath;
};

let index = 0;
/**
 * 添加日志
 * @param {Object} log 日志对象
 * @param {string} logType 日志类型
 */
export const addLog = (log, logType = LOG_TYPE.INFO) => {
  const logFileName = `[${index++}] ${logType} ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`;
  const logDirPath = getLogPath();

  if (!fs.existsSync(logDirPath)) {
    fs.mkdirSync(logDirPath, { recursive: true });
  }

  writeFile(path.join(logDirPath, `${logFileName}.json`), JSON.stringify(log, null, 2));
};

/**
 * 清空日志
 */
export const emptyLog = () => {
  index = 0;
  const logPath = getLogPath();
  const logFiles = fs.readdirSync(logPath);
  logFiles.forEach((file) => {
    fs.unlinkSync(path.join(logPath, file));
  });
};

import path from 'path';
import fs from 'fs';
import dayjs from 'dayjs';
import { getDirPath, writeFile } from '../files/index.mjs';
import { LOG_TYPE } from '../config/enum/index.js';

const getLogPath = () => {
  const logPath = path.join(getDirPath(), '../../logs');
  return process.env.LOG_PATH || logPath;
};

// 存储当前会话的所有日志
let sessionLogs = [];
let index = 0;
let sessionId = null;
// 存储待匹配的请求，用于关联请求和响应
let pendingRequests = new Map();

/**
 * 初始化新的日志会话
 */
export const initLogSession = () => {
  sessionLogs = [];
  index = 0;
  sessionId = dayjs().format('YYYY-MM-DD_HH-mm-ss');
  pendingRequests.clear();
};



/**
 * 添加日志到当前会话
 * @param {Object} log 日志对象
 * @param {string} logType 日志类型
 * @param {string} requestId 可选的请求ID，用于直接关联
 */
export const addLog = (log, logType = LOG_TYPE.INFO, requestId = null) => {
  const timestamp = dayjs().format('YYYY-MM-DD HH:mm:ss');
  let logEntry = {
    index: index++,
    type: logType,
    timestamp,
    data: log
  };

  // 处理请求-响应关联
  if (logType === LOG_TYPE.LLM_REQUEST) {
    // 只有提供了requestId才进行关联处理
    if (requestId) {
      logEntry.requestId = requestId;

      // 存储请求信息，等待响应匹配
      pendingRequests.set(requestId, {
        index: logEntry.index,
        timestamp,
        model: log.model
      });
    }

  } else if (logType === LOG_TYPE.LLM_RESPONSE) {
    // 只有提供了requestId才进行关联
    const responseId = log.id;

    if (requestId) {
      logEntry.requestId = requestId;
      logEntry.responseId = responseId;

      // 更新对应的请求日志，添加响应信息
      const requestLog = sessionLogs.find(entry => entry.requestId === requestId);
      if (requestLog) {
        requestLog.responseId = responseId;
        requestLog.responseIndex = logEntry.index;
      }

      // 从待匹配列表中移除
      pendingRequests.delete(requestId);
    } else {
      // 如果没有提供requestId，只记录responseId，不进行关联
      logEntry.responseId = responseId;
      logEntry.unmatched = true;
    }

  } else if (logType === LOG_TYPE.LLM_ERROR) {
    // 只有提供了requestId才进行关联
    if (requestId) {
      logEntry.requestId = requestId;

      // 更新对应的请求日志
      const requestLog = sessionLogs.find(entry => entry.requestId === requestId);
      if (requestLog) {
        requestLog.hasError = true;
        requestLog.errorIndex = logEntry.index;
      }

      // 从待匹配列表中移除
      pendingRequests.delete(requestId);
    }
    // 如果没有提供requestId，不进行关联，只记录错误日志
  }

  sessionLogs.push(logEntry);
};

/**
 * 保存当前会话的所有日志到文件
 */
export const saveSessionLogs = () => {
  if (sessionLogs.length === 0) {
    return;
  }

  const logDirPath = getLogPath();
  if (!fs.existsSync(logDirPath)) {
    fs.mkdirSync(logDirPath, { recursive: true });
  }

  const logFileName = `session_${sessionId || dayjs().format('YYYY-MM-DD_HH-mm-ss')}.json`;
  const logFilePath = path.join(logDirPath, logFileName);

  const sessionData = {
    sessionId: sessionId || dayjs().format('YYYY-MM-DD_HH-mm-ss'),
    startTime: sessionLogs[0]?.timestamp,
    endTime: sessionLogs[sessionLogs.length - 1]?.timestamp,
    totalLogs: sessionLogs.length,
    logs: sessionLogs
  };

  // 添加请求-响应关联统计信息
  const stats = getSessionStats();
  sessionData.stats = stats;

  writeFile(logFilePath, JSON.stringify(sessionData, null, 2));
  console.log(`日志已保存到: ${logFilePath}`);
  console.log(`会话统计: ${stats.totalRequests} 个请求, ${stats.matchedRequests} 个已匹配, ${stats.unmatchedRequests} 个未匹配`);
};

/**
 * 获取当前会话的统计信息
 */
const getSessionStats = () => {
  const requests = sessionLogs.filter(log => log.type === LOG_TYPE.LLM_REQUEST);
  const responses = sessionLogs.filter(log => log.type === LOG_TYPE.LLM_RESPONSE);
  const errors = sessionLogs.filter(log => log.type === LOG_TYPE.LLM_ERROR);

  const matchedRequests = requests.filter(req => req.responseId || req.hasError);
  const unmatchedRequests = requests.filter(req => !req.responseId && !req.hasError);
  const unmatchedResponses = responses.filter(res => res.unmatched);

  return {
    totalRequests: requests.length,
    totalResponses: responses.length,
    totalErrors: errors.length,
    matchedRequests: matchedRequests.length,
    unmatchedRequests: unmatchedRequests.length,
    unmatchedResponses: unmatchedResponses.length,
    pendingRequests: pendingRequests.size
  };
};

/**
 * 清空日志
 */
export const emptyLog = () => {
  sessionLogs = [];
  index = 0;
  sessionId = null;
  pendingRequests.clear();

  const logPath = getLogPath();
  if (!fs.existsSync(logPath)) {
    return;
  }

  const logFiles = fs.readdirSync(logPath);
  logFiles.forEach((file) => {
    fs.unlinkSync(path.join(logPath, file));
  });
};

import path from 'path';
import fs from 'fs';
import dayjs from 'dayjs';
import { getDirPath, writeFile } from '../files/index.mjs';
import { LOG_TYPE } from '../config/enum/index.js';

const getLogPath = () => {
  const logPath = path.join(getDirPath(), '../../logs');
  return process.env.LOG_PATH || logPath;
};

// 存储当前会话的所有日志
let sessionLogs = [];
let index = 0;
let sessionId = null;

/**
 * 初始化新的日志会话
 */
export const initLogSession = () => {
  sessionLogs = [];
  index = 0;
  sessionId = dayjs().format('YYYY-MM-DD_HH-mm-ss');
};

/**
 * 添加日志到当前会话
 * @param {Object} log 日志对象
 * @param {string} logType 日志类型
 */
export const addLog = (log, logType = LOG_TYPE.INFO) => {
  const logEntry = {
    index: index++,
    type: logType,
    timestamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    data: log
  };

  sessionLogs.push(logEntry);
};

/**
 * 保存当前会话的所有日志到文件
 */
export const saveSessionLogs = () => {
  if (sessionLogs.length === 0) {
    return;
  }

  const logDirPath = getLogPath();
  if (!fs.existsSync(logDirPath)) {
    fs.mkdirSync(logDirPath, { recursive: true });
  }

  const logFileName = `session_${sessionId || dayjs().format('YYYY-MM-DD_HH-mm-ss')}.json`;
  const logFilePath = path.join(logDirPath, logFileName);

  const sessionData = {
    sessionId: sessionId || dayjs().format('YYYY-MM-DD_HH-mm-ss'),
    startTime: sessionLogs[0]?.timestamp,
    endTime: sessionLogs[sessionLogs.length - 1]?.timestamp,
    totalLogs: sessionLogs.length,
    logs: sessionLogs
  };

  writeFile(logFilePath, JSON.stringify(sessionData, null, 2));
  console.log(`日志已保存到: ${logFilePath}`);
};

/**
 * 清空日志
 */
export const emptyLog = () => {
  sessionLogs = [];
  index = 0;
  sessionId = null;

  const logPath = getLogPath();
  if (!fs.existsSync(logPath)) {
    return;
  }

  const logFiles = fs.readdirSync(logPath);
  logFiles.forEach((file) => {
    fs.unlinkSync(path.join(logPath, file));
  });
};

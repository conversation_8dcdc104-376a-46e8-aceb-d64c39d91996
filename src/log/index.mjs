import path from 'path';
import fs from 'fs';
import dayjs from 'dayjs';
import { getDirPath, writeFile } from '../files/index.mjs';
import { LOG_TYPE } from '../config/enum/index.js';

const getLogPath = () => {
  const logPath = path.join(getDirPath(), '../../logs');
  return process.env.LOG_PATH || logPath;
};

// 存储当前会话的所有日志
let sessionLogs = [];
let index = 0;
let sessionId = null;
// 存储待匹配的请求，用于关联请求和响应
let pendingRequests = new Map();

/**
 * 初始化新的日志会话
 */
export const initLogSession = () => {
  sessionLogs = [];
  index = 0;
  sessionId = dayjs().format('YYYY-MM-DD_HH-mm-ss');
  pendingRequests.clear();
};

/**
 * 生成唯一的请求ID
 */
const generateRequestId = () => {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * 添加日志到当前会话
 * @param {Object} log 日志对象
 * @param {string} logType 日志类型
 */
export const addLog = (log, logType = LOG_TYPE.INFO) => {
  const timestamp = dayjs().format('YYYY-MM-DD HH:mm:ss');
  let logEntry = {
    index: index++,
    type: logType,
    timestamp,
    data: log
  };

  // 处理请求-响应关联
  if (logType === LOG_TYPE.LLM_REQUEST) {
    // 为请求生成唯一ID
    const requestId = generateRequestId();
    logEntry.requestId = requestId;

    // 存储请求信息，等待响应匹配
    pendingRequests.set(requestId, {
      index: logEntry.index,
      timestamp,
      model: log.model
    });

  } else if (logType === LOG_TYPE.LLM_RESPONSE) {
    // 尝试匹配响应到对应的请求
    const responseId = log.id;
    let matchedRequestId = null;

    // 查找匹配的请求（通过模型和时间窗口匹配）
    for (const [reqId, reqInfo] of pendingRequests.entries()) {
      // 简单的匹配策略：找到最近的未匹配请求
      if (reqInfo.model === log.model) {
        matchedRequestId = reqId;
        break;
      }
    }

    if (matchedRequestId) {
      logEntry.requestId = matchedRequestId;
      logEntry.responseId = responseId;

      // 更新对应的请求日志，添加响应信息
      const requestLog = sessionLogs.find(entry => entry.requestId === matchedRequestId);
      if (requestLog) {
        requestLog.responseId = responseId;
        requestLog.responseIndex = logEntry.index;
      }

      // 从待匹配列表中移除
      pendingRequests.delete(matchedRequestId);
    } else {
      // 如果没有找到匹配的请求，标记为未匹配
      logEntry.unmatched = true;
      logEntry.responseId = responseId;
    }

  } else if (logType === LOG_TYPE.LLM_ERROR) {
    // 错误日志也尝试关联到最近的请求
    let matchedRequestId = null;
    for (const [reqId] of pendingRequests.entries()) {
      matchedRequestId = reqId;
      break; // 取最近的一个请求
    }

    if (matchedRequestId) {
      logEntry.requestId = matchedRequestId;

      // 更新对应的请求日志
      const requestLog = sessionLogs.find(entry => entry.requestId === matchedRequestId);
      if (requestLog) {
        requestLog.hasError = true;
        requestLog.errorIndex = logEntry.index;
      }

      // 从待匹配列表中移除
      pendingRequests.delete(matchedRequestId);
    }
  }

  sessionLogs.push(logEntry);
};

/**
 * 保存当前会话的所有日志到文件
 */
export const saveSessionLogs = () => {
  if (sessionLogs.length === 0) {
    return;
  }

  const logDirPath = getLogPath();
  if (!fs.existsSync(logDirPath)) {
    fs.mkdirSync(logDirPath, { recursive: true });
  }

  const logFileName = `session_${sessionId || dayjs().format('YYYY-MM-DD_HH-mm-ss')}.json`;
  const logFilePath = path.join(logDirPath, logFileName);

  const sessionData = {
    sessionId: sessionId || dayjs().format('YYYY-MM-DD_HH-mm-ss'),
    startTime: sessionLogs[0]?.timestamp,
    endTime: sessionLogs[sessionLogs.length - 1]?.timestamp,
    totalLogs: sessionLogs.length,
    logs: sessionLogs
  };

  // 添加请求-响应关联统计信息
  const stats = getSessionStats();
  sessionData.stats = stats;

  writeFile(logFilePath, JSON.stringify(sessionData, null, 2));
  console.log(`日志已保存到: ${logFilePath}`);
  console.log(`会话统计: ${stats.totalRequests} 个请求, ${stats.matchedRequests} 个已匹配, ${stats.unmatchedRequests} 个未匹配`);
};

/**
 * 获取当前会话的统计信息
 */
const getSessionStats = () => {
  const requests = sessionLogs.filter(log => log.type === LOG_TYPE.LLM_REQUEST);
  const responses = sessionLogs.filter(log => log.type === LOG_TYPE.LLM_RESPONSE);
  const errors = sessionLogs.filter(log => log.type === LOG_TYPE.LLM_ERROR);

  const matchedRequests = requests.filter(req => req.responseId || req.hasError);
  const unmatchedRequests = requests.filter(req => !req.responseId && !req.hasError);
  const unmatchedResponses = responses.filter(res => res.unmatched);

  return {
    totalRequests: requests.length,
    totalResponses: responses.length,
    totalErrors: errors.length,
    matchedRequests: matchedRequests.length,
    unmatchedRequests: unmatchedRequests.length,
    unmatchedResponses: unmatchedResponses.length,
    pendingRequests: pendingRequests.size
  };
};

/**
 * 清空日志
 */
export const emptyLog = () => {
  sessionLogs = [];
  index = 0;
  sessionId = null;
  pendingRequests.clear();

  const logPath = getLogPath();
  if (!fs.existsSync(logPath)) {
    return;
  }

  const logFiles = fs.readdirSync(logPath);
  logFiles.forEach((file) => {
    fs.unlinkSync(path.join(logPath, file));
  });
};

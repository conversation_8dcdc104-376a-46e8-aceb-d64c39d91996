import chalk from 'chalk';
/**
 * 输出日志
 */

//本地日期时间
function time() {
  let t = new Date();
  return chalk.gray(`${t.getHours()}:${t.getMinutes().toString().padStart(2, '0')}:${t.getSeconds()}.${t.getMilliseconds()}`);
}

//输出信息
export const info = (...args) => {
  console.info.call(null, chalk.green('[INFO]'), time(), ...args);
};

//输出错误
export const error = (...args) => {
  console.error.call(null, chalk.red('[ERROR]'), time(), ...args);
};

//输出调试数据（仅在开发模式下输出）
const debug = (...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.error.call(null, chalk.gray('[DEBUG]'), time(),
      ...args.map(arg => typeof arg === 'string' ? chalk.gray(arg) : arg));
  }
};

//输出警告信息
export const warn = (...args) => {
  console.warn.call(null, chalk.yellow('[WARN]'), time(), ...args);
};

//输出成功信息
export const success = (...args) => {
  console.log.call(null, chalk.green('[SUCCESS]'), time(), ...args);
};

export { debug };
export const log = debug;

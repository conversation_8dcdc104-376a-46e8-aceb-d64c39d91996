# YDP标签完整层级关系和属性支持文档

## 概述
YDP (YDYD Page) 是一种基于XML的页面描述语言，通过声明式的标签和属性配置来生成HTML页面。本文档详细描述了YDP标签的完整层级关系和每个标签支持的属性。

## 1. YDP标签完整层级关系

### 根层级结构
```
page (根节点)
├── paras (参数设置)
│   └── para (单个参数)
├── data_source (数据源)
│   └── sql (SQL查询)
├── iframes (隐式提交框架)
└── form (表单)
    ├── script (脚本)
    └── table (表格)
        ├── th (列宽定义)
        │   └── td (列宽值)
        └── tr (表格行)
            ├── EXT_LINETAG (动态扩展行配置 - 当 EXT_IsEnable="true" 时)
            │   ├── EXT_fdListDic (字段列表字典配置)
            │   │   └── row (字段配置行)
            │   └── EXT_strSql (扩展行SQL查询)
            └── td (表格单元格)
                ├── style (样式)
                ├── value (显示值)
                ├── td-child (子单元格 - 用于跨列单元格内部)
                │   ├── style (子单元格样式)
                │   └── value (子单元格显示值)
                └── input (输入控件)
                    ├── style (控件样式)
                    ├── option (选项 - 用于select/radio/checkbox)
                    ├── option_single (单级字典选项)
                    ├── map (映射关系)
                    ├── event (事件)
                    └── foregrounds/backgrounds (前景色/背景色组)
```

## 2. 各标签支持的属性详解

### page标签属性
| 属性 | 说明 | 示例 |
|------|------|------|
| `row-count` | 页面行数 | `row-count="5"` |
| `column-count` | 页面列数 | `column-count="5"` |

### form标签属性
| 属性 | 说明 | 示例 |
|------|------|------|
| `name` | 表单名称 | `name="mainForm"` |
| `action` | 提交地址 | `action="/submit"` |
| `title` | 页面标题 | `title="用户管理"` |
| `target` | 提交目标 | `target="_self"` |
| `class` | 样式类名 | `class="default"` |

### table标签属性
| 属性 | 说明 | 示例 |
|------|------|------|
| `align` | 对齐方式 | `align="0"` |
| `height` | 表格高度 | `height="400"` |

### tr标签属性
| 属性 | 说明 | 示例 |
|------|------|------|
| `height` | 行高 | `height="16"` |
| `ispage` | 是否分页显示 | `ispage="true"` |
| `dynamictag` | 是否动态行 | `dynamictag="true"` |
| `EXT_IsEnable` | 是否启用扩展行 | `EXT_IsEnable="true"` |
| `row` | 行号 | `row="0"` |
| `class` | 样式类名 | `class="InputTrji"` |

### td标签属性
| 属性 | 说明 | 示例 |
|------|------|------|
| `span` | 是否跨列 | `span="false"` |
| `type` | 单元格类型 | `type="text"` |
| `hiddenExp` | 隐藏表达式 | `hiddenExp="=status=='hidden'"` |
| `name` | 名称 | `name="userName"` |
| `rowspan` | 跨行数 | `rowspan="2"` |
| `colspan` | 跨列数 | `colspan="3"` |
| `col` | 列号 | `col="0"` |
| `align` | 对齐方式 | `align="center"` |

### td-child标签属性
| 属性 | 说明 | 示例 |
|------|------|------|
| `row` | 所属行号 | `row="1"` |
| `type` | 单元格类型 | `type="common"` |
| `hiddenExp` | 隐藏表达式 | `hiddenExp="=status=='hidden'"` |
| `name` | 名称 | `name="childCell"` |
| `rowspan` | 跨行数 | `rowspan="1"` |
| `colspan` | 跨列数 | `colspan="1"` |
| `col` | 列号 | `col="0"` |

**注意**: `td-child`标签在当前转换逻辑中**未被处理**，不会生成HTML输出。它主要用于表示跨列单元格内部的逻辑子单元格结构。

## 3. input控件类型及属性

### 基础文本类控件

#### text/hidden/password
```xml
<input type="text" name="fieldName" value="=expression">
```

**支持属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `name` | 控件名称 | `name="userName"` |
| `value` | 控件值(支持表达式) | `value="=user.name"` |
| `maxlength` | 最大长度 | `maxlength="50"` |
| `size` | 显示宽度 | `size="20"` |
| `readOnlyExp` | 只读表达式 | `readOnlyExp="=user.locked"` |
| `hiddenExp` | 隐藏表达式 | `hiddenExp="=user.type=='admin'"` |
| `sign` | 是否签名 | `sign="true"` |
| `_required` | 是否必填 | `_required="true"` |
| `_type` | 验证类型 | `_type="string/int/money/date"` |
| `_desc` | 描述信息 | `_desc="用户名"` |
| `_maxlength` | 最大长度限制 | `_maxlength="100"` |
| `_minlength` | 最小长度限制 | `_minlength="6"` |
| `_maxvalue` | 最大值限制 | `_maxvalue="999999"` |
| `_minvalue` | 最小值限制 | `_minvalue="0"` |
| `_pwd2` | 密码确认字段 | `_pwd2="confirmPwd"` |
| `_samevaluefield` | 相同值字段 | `_samevaluefield="email"` |
| `allowNegative` | 是否允许负数 | `allowNegative="false"` |
| `_delNum` | 删除编号 | `_delNum="1"` |

#### textarea
```xml
<input type="textarea" name="content" rows="5" cols="50">
```

**额外属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `rows` | 行数 | `rows="5"` |
| `cols` | 列数 | `cols="50"` |

### 选择类控件

#### select (下拉框)
```xml
<input type="select" name="category" dictype="D">
  <option label="选项1" value="1"/>
  <option_single typeid="dict_type"/>
</input>
```

**专用属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `dictype` | 数据源类型 | `dictype="D"` (D=多级字典,E=自定义,T=静态) |
| `sql` | 数据查询SQL | `sql="SELECT * FROM dict"` |
| `datasourcename` | 数据源名称 | `datasourcename="workflow.cfg.xml"` |
| `viewname` | 视图名称 | `viewname="user_view"` |
| `levelnum` | 字典级次 | `levelnum="3"` |
| `sorttype` | 排序方式 | `sorttype="H"` (H=横排,V=纵排) |
| `beginvalue` | 起始值 | `beginvalue="1"` |
| `isSuperCombo` | 是否超级下拉框 | `isSuperCombo="true"` |

#### radio (单选框)
```xml
<input type="radio" name="gender" dictype="T">
  <option label="男" value="M"/>
  <option label="女" value="F"/>
</input>
```

#### checkbox (复选框)
```xml
<input type="checkbox" name="hobbies" dictype="T">
  <option label="读书" value="reading"/>
  <option label="运动" value="sports"/>
</input>
```

### 特殊控件类型

#### button (按钮)
```xml
<input type="button" name="saveBtn" value="保存" _type="submit">
  <event eventType="onClick" eventScript="saveData();"/>
</input>
```

**专用属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `_type` | 按钮类型 | `_type="submit/return/reset/excel/pdf/word"` |
| `isframe` | 是否隐式提交 | `isframe="true"` |
| `iframenum` | 隐式提交编号 | `iframenum="1"` |
| `iframeisret` | 是否返回信息 | `iframeisret="true"` |
| `iframeretmes` | 返回信息 | `iframeretmes="操作成功"` |

#### date (日期控件)
```xml
<input type="date" name="birthday" initVal="2023-01-01" isView="false">
</input>
```

**专用属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `initVal` | 初始值 | `initVal="2023-01-01"` |
| `isView` | 是否只读 | `isView="false"` |

#### area (地区选择器)
```xml
<input type="area" name="region" typeID="area_code" level="3">
</input>
```

**专用属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `typeID` | 地区类型ID | `typeID="area_code"` |
| `level` | 级别 | `level="3"` |

#### mld (多级字典)
```xml
<input type="mld" name="category" typeID="product_type" level="3">
</input>
```

**专用属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `typeID` | 字典类型ID | `typeID="product_type"` |
| `level` | 字典级别 | `level="3"` |

#### image (图像)
```xml
<input type="image" name="photo" src="=imagePath" width="100" height="80">
</input>
```

**专用属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `src` | 图片路径 | `src="=imagePath"` |
| `width` | 宽度 | `width="100"` |
| `height` | 高度 | `height="80"` |
| `border` | 边框 | `border="1"` |
| `alt` | 替代文本 | `alt="用户头像"` |
| `hspace` | 水平间距 | `hspace="5"` |
| `vspace` | 垂直间距 | `vspace="5"` |
| `hrefExp` | 链接表达式 | `hrefExp="=linkUrl"` |

#### number (数字选择器)
```xml
<input type="number" name="quantity" startvalue="1" endvalue="100" stepvalue="1">
</input>
```

**专用属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `startvalue` | 起始值 | `startvalue="1"` |
| `endvalue` | 结束值 | `endvalue="100"` |
| `stepvalue` | 步长值 | `stepvalue="1"` |

#### graph (统计图)
```xml
<input type="graph" name="chart" graphtype="line" xtitle="时间" ytitle="数量">
</input>
```

**专用属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `graphtype` | 图表类型 | `graphtype="line/bar/pie"` |
| `xtitle` | X轴标题 | `xtitle="时间"` |
| `ytitle` | Y轴标题 | `ytitle="数量"` |
| `timetype` | 时间精度 | `timetype="day"` |
| `ispercent` | 是否百分比 | `ispercent="true"` |
| `isaverage` | 是否平均值 | `isaverage="true"` |
| `graphwidth` | 图表宽度 | `graphwidth="600"` |
| `grahpheight` | 图表高度 | `grahpheight="400"` |

#### tree (树形结构)
```xml
<input type="tree" name="orgTree" sql="=treeSQL" prValue="root">
</input>
```

**专用属性:**
| 属性 | 说明 | 示例 |
|------|------|------|
| `sql` | 树形数据SQL | `sql="=treeSQL"` |
| `prValue` | 根节点值 | `prValue="root"` |
| `desc` | 描述字段 | `desc="name"` |
| `isWholePath` | 是否全路径 | `isWholePath="true"` |

#### edoc (电子文档)
```xml
<input type="edoc" name="document">
</input>
```

## 4. 样式属性 (style标签内)

### 字体样式
| 属性 | 说明 | 示例 |
|------|------|------|
| `font-family` | 字体族 | `font-family="Arial"` |
| `font-family-num` | 字体族编号 | `font-family-num="1"` |
| `font-size` | 字体大小 | `font-size="14px"` |
| `font-size-num` | 字体大小编号 | `font-size-num="2"` |
| `font-weight` | 是否粗体 | `font-weight="true"` |
| `font-style` | 是否斜体 | `font-style="true"` |
| `text-decoration` | 文本装饰 | `text-decoration="true"` |

### 对齐和颜色
| 属性 | 说明 | 示例 |
|------|------|------|
| `text-align` | 文本对齐 | `text-align="center"` |
| `background-color` | 背景色 | `background-color="#FFFFFF"` |
| `color` | 前景色 | `color="#000000"` |

### 边框样式
| 属性 | 说明 | 示例 |
|------|------|------|
| `border-top-color` | 上边框颜色 | `border-top-color="#000000"` |
| `border-top-style` | 上边框样式 | `border-top-style="solid"` |
| `border-top-width` | 上边框宽度 | `border-top-width="1px"` |
| `border-bottom-color` | 下边框颜色 | `border-bottom-color="#000000"` |
| `border-bottom-style` | 下边框样式 | `border-bottom-style="solid"` |
| `border-bottom-width` | 下边框宽度 | `border-bottom-width="1px"` |
| `border-left-color` | 左边框颜色 | `border-left-color="#000000"` |
| `border-left-style` | 左边框样式 | `border-left-style="solid"` |
| `border-left-width` | 左边框宽度 | `border-left-width="1px"` |
| `border-right-color` | 右边框颜色 | `border-right-color="#000000"` |
| `border-right-style` | 右边框样式 | `border-right-style="solid"` |
| `border-right-width` | 右边框宽度 | `border-right-width="1px"` |

## 5. 事件属性 (event标签)
| 属性 | 说明 | 示例 |
|------|------|------|
| `eventType` | 事件类型 | `eventType="onClick/onBlur/onChange"` |
| `eventScript` | 事件脚本 | `eventScript="validateForm();"` |

## 6. 数据源属性 (data_source标签)
| 属性 | 说明 | 示例 |
|------|------|------|
| `key` | 数据集键名 | `key="userList"` |
| `isext` | 是否横向扩展 | `isext="true"` |
| `extvalue` | 扩展键值 | `extvalue="userId"` |
| `maxrows` | 最大行数 | `maxrows="100"` |
| `scope_all` | 是否全局有效 | `scope_all="true"` |
| `datasourcename` | 数据源名称 | `datasourcename="workflow.cfg.xml"` |

## 7. 特殊功能属性

### 分页相关
| 属性 | 说明 | 示例 |
|------|------|------|
| `ispage` | 是否分页 | `ispage="true"` |
| `currentpageno` | 当前页号 | `currentpageno="1"` |

### 动态行相关
| 属性 | 说明 | 示例 |
|------|------|------|
| `dynamictag` | 是否动态行 | `dynamictag="true"` |
| `EXT_IsEnable` | 是否启用扩展 | `EXT_IsEnable="true"` |
| `EXT_NAME` | 扩展名称 | `EXT_NAME="dynamicRows"` |
| `EXT_strSql` | 扩展SQL | `EXT_strSql="SELECT * FROM table"` |

### 验证相关
| 属性 | 说明 | 示例 |
|------|------|------|
| `_required` | 必填验证 | `_required="true"` |
| `_type` | 数据类型验证 | `_type="string/int/money/date"` |
| `_maxlength` | 最大长度验证 | `_maxlength="100"` |
| `_minlength` | 最小长度验证 | `_minlength="6"` |
| `_maxvalue` | 最大值验证 | `_maxvalue="999999"` |
| `_minvalue` | 最小值验证 | `_minvalue="0"` |

## 8. 表达式支持

YDP支持在属性值中使用表达式，表达式以`=`开头：

### 常用表达式示例
- `value="=user.name"` - 获取DataPool中的值
- `readOnlyExp="=user.status == 'locked'"` - 条件表达式
- `hiddenExp="=user.type != 'admin'"` - 隐藏条件
- `href="=baseUrl + '/detail?id=' + record.id"` - 链接拼接
- `value="=#[1].fieldName"` - 循环行数据引用

### 循环处理
在表格行中，可以使用`#`符号表示当前行号，系统会自动替换为实际的行号进行数据绑定。

## 9. 转换限制和注意事项

### 未处理的标签
- **`td-child`标签**: 虽然在常量中定义，但在转换逻辑中未被处理，不会生成HTML输出
- **用途**: 主要用于表示跨列单元格内部的逻辑结构，帮助理解单元格的层级关系

### 使用示例
```xml
<!-- td-child的典型使用场景 -->
<td span="true" colspan="3" col="0">
    <value>主单元格内容</value>
    <td-child row="1" col="0">
        <value>逻辑子单元格1</value>
    </td-child>
    <td-child row="1" col="1">
        <value>逻辑子单元格2</value>
    </td-child>
    <td-child row="1" col="2">
        <value>逻辑子单元格3</value>
    </td-child>
</td>
```

## 10. YDP 标签属性详细整理（基于 HtmlModel.java 转换逻辑）

### 10.1 option 标签

**用途**: 用于 select、radio、checkbox 控件的选项定义

**转换处理**: 在 `DefaultProxy.processStaticInfo()` 和 `DefaultProxy.genMapOption()` 方法中处理

**支持属性**:
| 属性 | 说明 | 可能值 | 示例 | 转换逻辑 |
|------|------|--------|------|----------|
| `label` | 显示文本 | 任意文本或表达式 | `label="管理员"` | 通过 `HtmlConstants.OPTION_LABEL` 获取 |
| `value` | 选项值 | 任意值或表达式 | `value="1"` | 通过 `HtmlConstants.OPTION_VALUE` 获取 |

**属性值类型**:
- **静态值**: 直接文本，如 `label="管理员"`, `value="1"`
- **表达式**: 以 `=` 开头，如 `label="=itemval"`, `value="=itemid"`
- **循环表达式**: 包含 `#` 的表达式，如 `value="=typeList[#].id"`，会自动循环处理

**转换逻辑**:
1. 在 `SelectProxy`、`RadioProxy`、`CheckBoxProxy` 构造函数中调用 `processStaticInfo()`
2. 检查属性值是否以 `=` 开头，如果是则作为表达式处理
3. 如果表达式包含 `#`，则进行循环处理直到数据为空
4. 最终转换为 HTML 的 `<option>` 标签

**使用示例**:
```xml
<!-- 静态选项 -->
<option label="请选择" value="-1" />
<option label="新建商品房" value="01" />

<!-- 动态表达式选项 -->
<option label="=itemval" value="=itemid" />

<!-- 循环表达式选项 -->
<option label="=typeList[#].name" value="=typeList[#].id" />
```

### 10.2 option_single 标签

**用途**: 用于单级字典选项，简化字典数据的引用

**转换处理**: 在 HtmlConstants 中定义为 `INPUT_OPTION_SINGLE = "option_single"`，但在当前代码中未找到具体的转换实现

**支持属性**:
| 属性 | 说明 | 可能值 | 示例 | 转换状态 |
|------|------|--------|------|----------|
| `typeid` | 字典类型ID | 字典类型标识符 | `typeid="dict_type"` | 常量已定义，转换逻辑待实现 |

**使用示例**:
```xml
<input type="select" name="category" dictype="D">
  <option label="选项1" value="1"/>
  <option_single typeid="dict_type"/>
</input>
```

### 10.3 map 标签

**用途**: 用于定义映射关系，在 input 控件内部使用

**转换处理**: 在 `DefaultProxy.genMapOption()` 方法中处理

**支持属性**:
| 属性 | 说明 | 可能值 | 示例 | 转换逻辑 |
|------|------|--------|------|----------|
| `label` | 映射显示文本 | 表达式 | `label="=itemval"` | 与 option 标签相同的处理逻辑 |
| `value` | 映射值 | 表达式 | `value="=itemid"` | 与 option 标签相同的处理逻辑 |

**转换逻辑**:
1. 在 `genMapOption()` 方法中处理，逻辑与 option 标签类似
2. 支持循环表达式（包含 `#` 的表达式）
3. 最终生成与 option 相同的数据结构

**注意**: 在 HtmlConstants 中定义为 `INPUT_MAP = "map"`，主要用于内部数据映射处理

### 10.4 EXT_LINETAG 标签

**用途**: 动态扩展行配置，用于定义可动态生成的表格行

**转换处理**: 在 `HtmlModel.genTRString()` 方法中处理，当 `EXT_IsEnable="true"` 时激活

**支持属性**:
| 属性 | 说明 | 可能值 | 示例 | 转换逻辑 |
|------|------|--------|------|----------|
| `extname` | 扩展名称 | 扩展行标识名 | `extname="dRow1"` | 通过 `HtmlConstants.EXT_NAME` 获取 |
| `data_source` | 数据源名称 | 数据源配置文件 | `data_source="vhfs.cfg.xml"` | 通过 `HtmlConstants.DATASOURCE` 获取 |

**子标签**:
- **EXT_fdListDic**: 字段列表字典配置，通过 `HtmlConstants.EXT_fdListDic` 获取
- **EXT_strSql**: 扩展行的 SQL 查询语句，通过 `HtmlConstants.EXT_strSql` 获取

**EXT_fdListDic 中 row 标签属性**:
| 属性 | 说明 | 可能值 | 示例 | 转换逻辑 |
|------|------|--------|------|----------|
| `EXT_fdname` | 字段名称 | 数据库字段名 | `EXT_fdname="accorg"` | 通过 `HtmlConstants.EXT_fdname` 获取 |
| `EXT_fddesc` | 字段描述 | 字段显示名称 | `EXT_fddesc="机构"` | 通过 `HtmlConstants.EXT_fddesc` 获取 |
| `EXT_fdIsEdit` | 是否可编辑 | `true`/`false` | `EXT_fdIsEdit="false"` | 通过 `HtmlConstants.EXT_fdIsEdit` 获取 |
| `EXT_fdIsHidden` | 是否隐藏 | `true`/`false` | `EXT_fdIsHidden="false"` | 通过 `HtmlConstants.EXT_fdIsHidden` 获取 |

**转换逻辑**:
1. 在 `genTRString()` 方法中检查 `EXT_IsEnable` 属性
2. 如果为 `true`，创建 `TableListDesc` 对象
3. 解析 `EXT_strSql` 中的 SQL 语句，通过 `ParseExpression.mathExp()` 处理
4. 处理 `EXT_fdListDic` 中的字段配置
5. 生成动态扩展行的 HTML 结构

**层级关系说明**:
EXT_LINETAG 标签位于 `<tr>` 标签的直接子级，与 `<td>` 标签平级，而不是在 `<input>` 标签内部。

**使用示例**:
```xml
<tr height="16" ispage="false" dynamictag="false" EXT_IsEnable="true" row="4">
    <EXT_LINETAG extname="dRow1" data_source="vhfs.cfg.xml">
        <EXT_fdListDic>
            <row EXT_fdIsHidden="false" EXT_fdIsEdit="false" EXT_fddesc="机构" EXT_fdname="accorg" />
            <row EXT_fdIsHidden="false" EXT_fdIsEdit="false" EXT_fddesc="账务日期" EXT_fdname="accdate" />
            <row EXT_fdIsHidden="false" EXT_fdIsEdit="false" EXT_fddesc="资金类型" EXT_fdname="souflag">
                &lt;dt&gt;sql&lt;/&gt;
                &lt;content&gt;SELECT ITEMID,ITEMVAL FROM MULTILEVELDIC WHERE TYPEID='yhdz_zjlx'&lt;/&gt;
                &lt;ds&gt;workflow.cfg.xml&lt;/&gt;
            </row>
        </EXT_fdListDic>
        <EXT_strSql>"select accorg,accdate,souflag,bankcode from ac035"</EXT_strSql>
    </EXT_LINETAG>
    <td span="false" type="common" hiddenExp="" name="" rowspan="1" colspan="1" col="0">
        <style />
        <value />
    </td>
    <!-- 更多 td 标签 -->
</tr>
```

### 10.5 转换流程总结

**调用链路**:
```
HtmlModel.showTdInput()
  → HtmlModel.createCtrlProxy()
    → SelectProxy/RadioProxy/CheckBoxProxy 构造函数
      → DefaultProxy.processStaticInfo()
        → 处理 option 标签属性
```

**表达式处理机制**:
- 以 `=` 开头表示表达式，通过 `ParseExpression.mathExp()` 解析
- 包含 `#` 的表达式会进行循环处理，通过 `StringUtil.replaceString()` 替换
- 支持数据源引用：`=dataSource.field[#]`
- 支持函数调用：`=getMultiDicVal('0','1680641',state)`

**字典配置格式**:
在 EXT_LINETAG 的字段配置中，可以包含特殊的字典配置：
```xml
<row>
  &lt;dt&gt;sql&lt;/&gt;
  &lt;content&gt;SELECT ITEMID,ITEMVAL FROM MULTILEVELDIC WHERE...&lt;/&gt;
  &lt;ds&gt;workflow.cfg.xml&lt;/&gt;
</row>
```

**常量配置格式**:
```xml
<row>
  &lt;dt&gt;const&lt;/&gt;
  &lt;content&gt;"0":"未到账","1":"已到账"&lt;/&gt;
  &lt;ds&gt;chdc.cfg.xml&lt;/&gt;
</row>
```

这些标签构成了 YDP 系统中数据选择和动态内容生成的核心机制，通过 HtmlModel.java 中的转换逻辑实现了从 YDP 到 HTML 的完整转换过程。

# YDP 转换逻辑文档

## 概述

YDP Helper 是一个用于将 YDP (原有页面定义) 文件转换为现代化页面格式的工具。转换过程包括解析 XML 格式的 YDP 文件，提取页面元素信息，并生成对应的 YDAP 文件和 Excel 文档。

## 转换流程

### 主要转换流程

```mermaid
flowchart TD
    A[开始转换] --> B[读取命令行参数]
    B --> C{参数类型判断}
    C -->|文件| D[处理单个YDP文件]
    C -->|目录| E[遍历目录中的YDP文件]
    E --> D
    D --> F[读取YDP文件内容]
    F --> G[字符编码转换 GBK→UTF-8]
    G --> H[XML转JSON解析]
    H --> I[解析页面元素]
    I --> J[生成JavaScript文件]
    J --> K[生成Excel文档]
    K --> L[生成YDAP文件]
    L --> M[更新统计信息]
    M --> N{还有文件?}
    N -->|是| D
    N -->|否| O[输出处理结果]
    O --> P[结束]
```

### 元素解析详细流程

```mermaid
flowchart TD
    A[开始解析元素] --> B[遍历XML元素数组]
    B --> C{元素类型判断}
    
    C -->|form| D[提取页面标题]
    C -->|input| E[解析表单输入字段]
    C -->|iframe| F[解析隐式提交]
    C -->|data_source| G[解析数据源]
    C -->|value| H[解析文本标签]
    C -->|script| I[解析JavaScript脚本]
    C -->|EXT_LINETAG| J[解析动态扩展行]
    C -->|其他| K[递归处理子元素]
    
    D --> L[保存到结果对象]
    E --> M[字段类型转换]
    M --> N[校验规则处理]
    N --> O[事件处理]
    O --> L
    
    F --> P[提取SQL查询条件]
    P --> Q[解析输入输出变量]
    Q --> L
    
    G --> R[解析数据集SQL]
    R --> S[提取查询条件变量]
    S --> L
    
    H --> T{是否为页面标题?}
    T -->|是| U[设置页面标题]
    T -->|否| V[处理标签文本]
    V --> W{是否包含列表标识?}
    W -->|是| X[解析静态列表]
    W -->|否| Y[处理普通文本]
    X --> L
    Y --> L
    U --> L
    
    I --> Z[解析隐式发报文]
    Z --> L
    
    J --> AA[解析动态列表结构]
    AA --> BB[提取列定义]
    BB --> L
    
    K --> B
    L --> CC{还有元素?}
    CC -->|是| B
    CC -->|否| DD[解析完成]
```

### 输出文件生成流程

```mermaid
flowchart TD
    A[开始生成输出] --> B{是否有JavaScript?}
    B -->|是| C[生成.js文件]
    B -->|否| D[准备Excel数据]
    C --> D
    
    D --> E[整理页面要素数据]
    E --> F[整理子事件数据]
    F --> G[使用Excel模板生成文档]
    G --> H[生成.xlsx文件]
    
    H --> I[开始生成YDAP]
    I --> J[构建表单字段]
    J --> K[处理隐藏字段]
    K --> L[处理按钮]
    L --> M{是否有动态列表?}
    M -->|是| N[添加动态列表块]
    M -->|否| O{是否有静态列表?}
    N --> O
    O -->|是| P[添加静态列表块]
    O -->|否| Q[设置事件动作]
    P --> Q
    Q --> R[生成.ydap文件]
    R --> S[转换完成]
```

## 核心数据结构

### 解析结果对象 (r)

转换过程中使用的核心数据结构：

```javascript
const r = {
  inputs: [],      // 表单字段信息
  ajaxs: [],       // 隐式提交信息  
  messages: [],    // 隐式发报文信息
  datasets: [],    // 数据集信息
  lists: [],       // 静态列表
  dynExtRows: [],  // 动态扩展行
  title: '',       // 页面标题
  lastLabel: '',   // 上一个标签
  javascript: ''   // js脚本
}
```

### 字段类型映射

| 原始类型 | 转换后类型 | 说明 |
|---------|-----------|------|
| text | Input | 普通输入框 |
| text + _type=int | Number | 正整数输入 |
| text + _type=number | Input + number校验 | 数字输入 |
| text + _type=money | Money | 金额输入 |
| text + _type=date | DateTime | 日期输入 |
| select | Select | 下拉选择 |
| radio | Radio | 单选框 |
| checkbox | Checkbox | 复选框 |
| textarea | TextArea | 多行文本 |
| password | Password | 密码输入 |
| button | Button | 按钮 |

## 特殊处理逻辑

### 1. 字符编码处理
- 输入：GBK 编码的 YDP 文件
- 转换：使用 iconv-lite 转换为 UTF-8

### 2. 表达式处理
- 删除前置等号：`=xxx` → `xxx`
- 删除标签尾部冒号：`标签：` → `标签`

### 3. SQL 查询条件处理
- 提取查询变量：`"+ variable +"` → `?`
- 区分变量类型：String 或 Number
- 处理模糊查询：`'%"+ variable +"%'` → `'%?%'`

### 4. 列表数据处理
- 静态列表：通过 `[#]` 标识识别
- 动态扩展行：通过 `EXT_LINETAG` 元素解析
- 列定义：提取列 ID 和标签

### 5. 事件处理
- 隐式提交：iframe 元素转换
- 隐式发报文：JavaScript 中的 sendHTMLMessage 函数
- 自定义事件：input 元素的 event 子元素

## 输出文件说明

### 1. .js 文件
- 包含原 YDP 中的 JavaScript 代码
- 处理回车换行符替换
- 清理多余空白行

### 2. .xlsx 文件  
- 使用预定义模板生成
- 包含三个工作表：总体说明、页面要素、子事件
- 提供详细的字段映射和配置信息

### 3. .ydap 文件
- JSON 格式的页面定义文件
- 包含页面结构、字段定义、事件配置
- 支持表单、列表、按钮等多种组件类型

## 错误处理

转换过程中的错误处理机制：
- 文件读取错误：记录错误并继续处理其他文件
- XML 解析错误：记录详细错误信息
- 编码转换错误：使用默认编码继续处理
- 统计信息：记录总数、成功数、失败数

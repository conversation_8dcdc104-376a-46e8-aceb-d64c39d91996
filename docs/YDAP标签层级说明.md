# YDAP标签完整层级关系和属性支持文档

## 概述
YDAP是一种基于JSON的现代页面描述语言，通过声明式的配置来生成React页面组件。本文档详细描述了YDAP的完整层级关系和每个层级支持的属性。

## 1. YDAP标签完整层级关系

### 根层级结构
```
page (根对象)
├── sections (章节集合)
│   └── section (单个章节)
│       ├── blocks (内容块集合)
│       │   └── block (单个内容块)
│       │       ├── fields (表单字段集合 - Form类型)
│       │       │   └── field (单个表单字段)
│       │       │       ├── rules (验证规则集合)
│       │       │       │   └── rule (单个验证规则)
│       │       │       ├── options (选项集合)
│       │       │       │   └── option (单个选项)
│       │       │       └── actions (字段动作集合)
│       │       │           └── action (单个动作)
│       │       ├── columns (列表列集合 - List类型)
│       │       │   └── column (单个列)
│       │       │       ├── options (列选项集合)
│       │       │       ├── rules (列验证规则集合)
│       │       │       └── actions (列动作集合)
│       │       ├── datasource (数据源配置)
│       │       ├── buttons (内容块按钮集合)
│       │       │   └── button (单个按钮)
│       │       │       └── actions (按钮动作集合)
│       │       └── actions (内容块动作集合)
│       └── buttons (章节按钮集合)
│           └── button (单个按钮)
├── modals (模态框集合)
│   └── modal (单个模态框)
│       ├── blocks (模态框内容块集合)
│       ├── shownActions/actionsshown (显示时动作集合)
│       ├── closeActions/actionsclose (关闭时动作集合)
│       └── closedActions/actionsclosed (关闭后动作集合)
├── actions (页面级动作集合)
│   └── action (单个页面动作)
├── emptystring (空字符串配置)
├── comparecond (对比条件配置)
├── asyncactions (异步动作配置)
└── performancemode (性能模式配置)
```

## 2. 各层级支持的属性详解

### 页面根对象属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `sections` | 章节集合 | Array/Object | `[{...}]` 或 `{...}` |
| `modals` | 模态框集合 | Array/Object | `[{...}]` 或 `{...}` |
| `actions` | 页面级动作集合 | Array | `[{type: "ajax", on: "load"}]` |
| `emptystring` | 空字符串清空字段值 | String | `"true"/"false"` |
| `comparecond` | 对比条件 | String | `"false"` |
| `asyncactions` | 异步动作配置 | String | `"false"` |
| `performancemode` | 性能模式 | String | `"true"/"false"` |

### 章节(section)属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `id` | 章节唯一标识 | String | `"section1"` |
| `title` | 章节标题 | String | `"用户信息"` |
| `icon` | 章节图标 | String | `"user"` |
| `hidden` | 是否隐藏 | String/Boolean | `"true"` |
| `collapsible` | 是否可折叠 | String/Boolean | `"true"` |
| `collapsed` | 是否默认折叠 | String/Boolean | `"false"` |
| `blocks` | 内容块集合 | Array/Object | `[{...}]` |
| `buttons` | 章节按钮集合 | Array | `[{...}]` |
| `layout` | 布局配置 | Object | `{gutter: 16}` |
| `span` | 栅格占位 | Number | `24` |
| `gutter` | 栅格间距 | Number | `16` |

### 内容块(block)属性

#### 通用属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `id` | 内容块唯一标识 | String | `"form1"` |
| `type` | 内容块类型 | String | `"Form"/"List"/"Tree"/"Tips"/"Compare"/"General"/"Custom"/"System"` |
| `title` | 内容块标题 | String | `"基本信息"` |
| `buttons` | 内容块按钮集合 | Array | `[{...}]` |
| `actions` | 内容块动作集合 | Array | `[{...}]` |

#### Form类型内容块属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `fields` | 表单字段集合 | Array | `[{...}]` |
| `layout` | 表单布局 | String | `"horizontal"/"vertical"/"inline"` |

#### List类型内容块属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `columns` | 列表列集合 | Array | `[{...}]` |
| `datasource` | 数据源配置 | Object/String | `{type: "ajax", url: "/api/data"}` |
| `checkbox` | 是否显示复选框 | String/Boolean | `"true"` |
| `rowbuttons` | 行按钮配置 | Array | `[{...}]` |
| `editmode` | 编辑模式 | String | `"none"/"inline"/"modal"/"batch"` |
| `batchsave` | 批量保存 | String/Boolean | `"true"` |
| `insert` | 允许新增 | String/Boolean | `"true"` |
| `delete` | 允许删除 | String/Boolean | `"true"` |
| `batchdelete` | 批量删除 | String/Boolean | `"true"` |
| `update` | 允许修改 | String/Boolean | `"true"` |
| `import` | 允许导入 | String/Boolean | `"true"` |
| `export` | 允许导出 | String/Boolean | `"true"` |
| `print` | 允许打印 | String/Boolean | `"true"` |

#### Tree类型内容块属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `readonly` | 是否只读 | String/Boolean | `"true"` |
| `datasource` | 树形数据源 | Object/String | `{type: "ajax", url: "/api/tree"}` |

#### Custom类型内容块属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `component` | 自定义组件名 | String | `"MyCustomComponent"` |
| `props` | 组件属性 | Object | `{prop1: "value1"}` |

### 表单字段(field)属性

#### 基础属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `id` | 字段唯一标识 | String | `"userName"` |
| `type` | 字段类型 | String | `"Text"/"Input"/"Select"/"Radio"/"Checkbox"等` |
| `label` | 字段标签 | String | `"用户名"` |
| `value` | 字段值 | String/Expression | `"defaultValue"` 或 `"=user.name"` |
| `placeholder` | 占位符 | String | `"请输入用户名"` |
| `readonly` | 是否只读 | String/Boolean | `"true"` |
| `hidden` | 是否隐藏 | String/Boolean | `"false"` |
| `required` | 是否必填 | String/Boolean | `"true"` |

#### 验证相关属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `rules` | 验证规则集合 | Array | `[{name: "required", message: "必填"}]` |
| `validatefirst` | 只验证第一个错误 | String/Boolean | `"true"` |

#### 选项相关属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `options` | 选项集合 | Array/Object | `[{value: "1", label: "选项1"}]` |
| `optiongroup` | 选项分组 | String | `"group1"` |
| `viewgroup` | 视图分组 | String | `"view1"` |

#### 数字相关属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `precision` | 精度 | Number | `2` |
| `min` | 最小值 | Number | `0` |
| `max` | 最大值 | Number | `100` |
| `step` | 步长 | Number | `1` |
| `allowNegative` | 允许负数 | String/Boolean | `"true"` |

#### 文本相关属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `maxlength` | 最大长度 | Number | `50` |
| `rows` | 文本域行数 | Number | `4` |
| `maxrows` | 最大行数 | Number | `8` |

#### 布局相关属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `span` | 栅格占位 | Number | `12` |
| `colspan` | 跨列数 | Number | `2` |
| `inline` | 行内显示 | String/Boolean | `"true"` |

#### 事件相关属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `actions` | 字段动作集合 | Array | `[{type: "ajax", on: "change"}]` |
| `asyncactions` | 异步动作集合 | Array | `[{...}]` |

### 列表列(column)属性

#### 基础属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `id` | 列唯一标识 | String | `"userName"` |
| `label` | 列标题 | String | `"用户名"` |
| `width` | 列宽度 | Number/String | `120` 或 `"120px"` |
| `type` | 列类型 | String | `"Text"/"Number"/"Select"等` |
| `align` | 对齐方式 | String | `"left"/"center"/"right"` |
| `fixed` | 固定列 | String | `"left"/"right"` |
| `hidden` | 是否隐藏 | String/Boolean | `"false"` |
| `ellipsis` | 超长省略 | String/Boolean | `"true"` |

#### 数据相关属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `value` | 列值表达式 | String/Expression | `"=record.userName"` |
| `format` | 格式化 | String | `"YYYY-MM-DD"` |
| `precision` | 数字精度 | Number | `2` |

#### 编辑相关属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `inline` | 行内编辑 | String/Boolean | `"true"` |
| `readonly` | 是否只读 | String/Boolean | `"false"` |
| `placeholder` | 占位符 | String | `"请输入"` |
| `rules` | 验证规则 | Array | `[{...}]` |

#### 排序和搜索属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `sort` | 是否可排序 | String/Boolean | `"true"` |
| `pinyin` | 拼音搜索 | String/Boolean | `"true"` |

### 按钮(button)属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `id` | 按钮唯一标识 | String | `"submitBtn"` |
| `type` | 按钮类型 | String | `"button"/"submit"/"reset"` |
| `text` | 按钮文本 | String | `"提交"` |
| `icon` | 按钮图标 | String | `"save"` |
| `size` | 按钮尺寸 | String | `"small"/"default"/"large"` |
| `shape` | 按钮形状 | String | `"default"/"circle"/"round"` |
| `loading` | 加载状态 | String/Boolean | `"false"` |
| `disabled` | 是否禁用 | String/Boolean | `"false"` |
| `hidden` | 是否隐藏 | String/Boolean | `"false"` |
| `actions` | 按钮动作集合 | Array | `[{type: "ajax", url: "/api/submit"}]` |
| `confirm` | 确认提示 | String | `"确定要提交吗？"` |
| `condition` | 显示条件 | String/Expression | `"=user.canEdit"` |

### 模态框(modal)属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `id` | 模态框唯一标识 | String | `"editModal"` |
| `title` | 模态框标题 | String | `"编辑用户"` |
| `size` | 模态框尺寸 | String | `"small"/"default"/"large"` |
| `width` | 自定义宽度 | Number/String | `800` |
| `height` | 自定义高度 | Number/String | `600` |
| `closable` | 是否可关闭 | String/Boolean | `"true"` |
| `maskClosable` | 点击遮罩关闭 | String/Boolean | `"true"` |
| `blocks` | 模态框内容块 | Array | `[{...}]` |
| `shownActions` | 显示时动作 | Array | `[{...}]` |
| `closeActions` | 关闭时动作 | Array | `[{...}]` |
| `closedActions` | 关闭后动作 | Array | `[{...}]` |

### 动作(action)属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `type` | 动作类型 | String | `"ajax"/"alert"/"showModal"等` |
| `on` | 触发事件 | String | `"click"/"change"/"load"/"shown"` |
| `cond` | 执行条件 | String/Expression | `"=user.canEdit"` |
| `async` | 是否异步 | String/Boolean | `"false"` |
| `url` | 请求地址(ajax类型) | String | `"/api/submit"` |
| `method` | 请求方法(ajax类型) | String | `"POST"/"GET"` |
| `data` | 请求数据(ajax类型) | Object/String | `{field1: "value1"}` |
| `success` | 成功回调动作 | Array | `[{...}]` |
| `error` | 失败回调动作 | Array | `[{...}]` |
| `message` | 提示消息 | String | `"操作成功"` |
| `confirm` | 确认提示 | String | `"确定要删除吗？"` |

### 验证规则(rule)属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `name` | 规则名称 | String | `"required"/"pattern"/"minLen"` |
| `value` | 规则值 | String/Number | `"true"` 或 `6` |
| `message` | 错误消息 | String | `"此字段为必填项"` |
| `trigger` | 触发时机 | String | `"blur"/"change"` |

### 选项(option)属性
| 属性 | 说明 | 类型 | 示例 |
|------|------|------|------|
| `value` | 选项值 | String/Number | `"1"` |
| `label` | 选项标签 | String | `"选项一"` |
| `disabled` | 是否禁用 | String/Boolean | `"false"` |

## 3. 表达式系统

### 表达式前缀
- `=` - 动态表达式，如 `"=user.name"`
- `#` - 循环行引用，如 `"=#[0].fieldName"`

### 常用表达式示例
- `value="=user.name"` - 获取数据池中的值
- `hidden="=user.type != 'admin'"` - 条件隐藏
- `readonly="=status == 'locked'"` - 条件只读
- `options="=getDictOptions('userType')"` - 动态选项
- `datasource="{type: 'ajax', url: '=baseUrl + \"/api/list\"'}"` - 动态数据源

## 4. 内容块类型详解

### Form - 表单内容块
用于展示和编辑表单数据，支持各种表单控件类型。

### List - 列表内容块  
用于展示表格数据，支持分页、排序、筛选、行内编辑等功能。

### Tree - 树形内容块
用于展示树形结构数据，支持展开/折叠、选择等操作。

### Tips - 提示内容块
用于展示提示信息、帮助文档等静态内容。

### Compare - 对比内容块
用于对比显示两组数据的差异。

### General - 通用内容块
用于展示自定义HTML内容。

### Custom - 自定义内容块
用于引入自定义React组件。

### System - 系统内容块
用于系统级功能组件。

## 5. 使用示例

### 基础页面结构
```json
{
  "sections": [
    {
      "id": "main",
      "title": "用户管理",
      "blocks": [
        {
          "type": "Form",
          "fields": [
            {
              "id": "userName",
              "type": "Input",
              "label": "用户名",
              "required": "true",
              "rules": [
                {"name": "required", "message": "用户名不能为空"}
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 带模态框的页面
```json
{
  "sections": [...],
  "modals": [
    {
      "id": "editModal",
      "title": "编辑用户",
      "blocks": [...],
      "shownActions": [
        {"type": "setFieldValue", "field": "userName", "value": "=selectedUser.name"}
      ]
    }
  ]
}
```

## 6. 转换机制

YDAP通过转译器将JSON配置转换为React JSX代码，支持：
- 动态表达式解析
- 事件处理绑定  
- 数据源集成
- 组件属性映射
- 验证规则转换
- 样式类名生成

## 7. 注意事项

### 兼容性处理
- 支持单个对象或数组格式的配置
- 向后兼容旧版本的属性名
- 自动处理属性类型转换

### 性能优化
- 支持性能模式配置
- 异步动作处理
- 组件懒加载

### 表达式安全
- 表达式沙箱执行
- XSS防护
- 数据验证

这个文档涵盖了YDAP的完整层级结构和属性配置，为开发者提供了全面的参考指南。

# YDP文件转换逻辑分析

## 概述
YDP文件是一种XML格式的页面描述文件，通过`HtmlModel`类的`expandElementNode`方法进行解析和转换，最终生成HTML页面。

## 转换入口
- **主要入口方法**: `HtmlModel.expandElementNode(Element node, Object parent)`
- **基础类**: `XMLReader` (抽象类)
- **解析流程**: XML → DOM → HTML

## 主要XML标签转换映射

### 1. 页面结构标签

| YDP标签 | 转换后HTML | 处理方法 | 说明 |
|---------|------------|----------|------|
| `<page>` | 无直接对应 | HtmlModel.expandElementNode | 根节点，触发子节点解析 |
| `<form>` | `<form>` | HtmlModel.execForm | 生成HTML表单，包含JavaScript脚本 |
| `<table>` | `<table>` | **未直接处理** | **execForm假设form子元素都是table，直接生成table标签** |
| `<tr>` | `<tr>` | HtmlModel.genTRString | 表格行 |
| `<td>` | `<td>` | HtmlModel.getTDHeadValue | 表格单元格 |
| `<th>` | `<colgroup>` | HtmlModel.genTHString | 列宽定义，转换为colgroup标签 |

### 2. 数据和参数标签

| YDP标签 | 转换后 | 处理方法 | 说明 |
|---------|--------|----------|------|
| `<paras>` | 无HTML输出 | HtmlModel.setParaToPool | 参数设置到DataPool |
| `<data_source>` | 无HTML输出 | HtmlModel.execDataSource | 数据源查询，结果存入DataPool |
| `<iframes>` | `<iframe>` | HtmlModel.expandElementNode | 设置isIFrame标志，在表单末尾生成iframe |

### 3. 输入控件标签转换

#### 文本输入类
| YDP input type | 转换后HTML | 代理类 |
|----------------|------------|--------|
| `type="text"` | `<input type="text">` | TextProxy |
| `type="hidden"` | `<input type="hidden">` | TextProxy |
| `type="password"` | `<input type="password">` | TextProxy |
| `type="textarea"` | `<textarea>` | TextAreaProxy |

#### 选择控件类
| YDP input type | 转换后HTML | 代理类 |
|----------------|------------|--------|
| `type="select"` | `<select>` | SelectProxy |
| `type="radio"` | `<input type="radio">` | RadioProxy |
| `type="checkbox"` | `<input type="checkbox">` | CheckBoxProxy |

#### 特殊控件类
| YDP input type | 转换后HTML | 代理类 | 说明 |
|----------------|------------|--------|------|
| `type="button"` | `<input type="button">` | ButtonProxy | 按钮控件，支持多种按钮类型 |
| `type="date"` | 日期选择器 | DateProxy | 日期选择控件 |
| `type="area"` | 地区选择器 | AreaProxy | 地区选择控件 |
| `type="mld"` | 多级字典 | MLDProxy | 多级字典控件 |
| `type="image"` | `<img>` | ImageProxy | 图片显示控件 |
| `type="number"` | 数字输入框 | NumberProxy | 数字选择控件 |
| `type="graph"` | 统计图表 | GraphProxy | 统计图表控件 |
| `type="tree"` | 树形结构 | TreeProxy | 树形结构控件 |
| `type="edoc"` | 电子文档 | EdocProxy | 电子文档控件 |
| `type="combobox"` | 组合框 | 默认TextProxy | 组合框控件（常量已定义但未使用） |

## 属性转换映射

### 4. 通用属性转换

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `name` | `name` | 控件名称 |
| `value` | `value` | 控件值 |
| `class` | `class` | CSS类名 |
| `align` | `align` | 对齐方式 |
| `height` | `height` | 高度 |
| `colspan` | `colspan` | 列跨度 |
| `rowspan` | `rowspan` | 行跨度 |
| `target` | `target` | 目标窗口 |
| `action` | `action` | 表单提交地址 |

### 5. 样式属性转换

| YDP样式属性 | CSS属性 | 转换说明 |
|-------------|---------|----------|
| `text-align` | `text-align` | 文本对齐，通过getAlign()方法转换 |
| `background-color` | `background-color` | 背景色，RGB值通过Tools.getHexFromColorRGB()转换为十六进制 |
| `border-*-color` | `border-*-color` | 边框颜色，RGB值转换为十六进制 |
| `border-*-style` | `border-*-style` | 边框样式，通过getBorderStyle()方法转换 |
| `border-*-width` | `border-*-width` | 边框宽度，直接使用原值 |
| `font-weight` | `font-weight` | 字体粗细，true转换为bold |
| `font-style` | `font-style` | 字体样式，true转换为italic |
| `font-size` | `font-size` | 字体大小，直接使用原值 |
| `font-family` | `font-family` | 字体族，直接使用原值 |
| `color` | `color` | 文字颜色，RGB值转换为十六进制 |
| `text-decoration` | `text-decoration` | 文本装饰，true转换为underline |

### 6. 验证和控制属性转换

| YDP验证属性 | HTML属性 | 说明 |
|-------------|----------|------|
| `_required` | `_required` | 必填验证 |
| `_maxlength` | `maxlength` | 最大长度 |
| `_minlength` | `_minlength` | 最小长度 |
| `_maxvalue` | `_maxvalue` | 最大值 |
| `_minvalue` | `_minvalue` | 最小值 |
| `_type` | `_type` | 数据类型验证 |
| `_desc` | `_desc` | 字段描述 |
| `_pwd2` | `_pwd2` | 密码确认字段 |
| `_samevaluefield` | `_samevaluefield` | 相同值字段验证 |
| `readOnlyExp` | `readonly` | 只读表达式 |
| `hiddenExp` | 控制显示/隐藏 | 隐藏表达式 |
| `sign` | `_sign` | 数字签名标记 |
| `allowNegative` | `allowNegative` | 是否允许负数 |

### 7. 文本框专用属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `maxlength` | `maxlength` | 最大输入长度 |
| `size` | `size` | 输入框宽度 |
| `readonly` | `readonly` | 只读状态 |
| `issign` | 签名相关 | 是否签名字段 |

### 8. 选择控件专用属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `dictype` | 数据源类型 | D=多级字典,E=自定义,T=静态 |
| `sql` | 数据查询 | SQL查询语句 |
| `datasourcename` | 数据源名称 | 数据库连接配置 |
| `isSuperCombo` | 超级下拉框 | 增强型下拉框 |

### 9. 数字控件专用属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `startvalue` | 起始值 | 数字选择起始值 |
| `endvalue` | 结束值 | 数字选择结束值 |
| `stepvalue` | 步长值 | 数字递增步长 |
| `initVal` | 初始值 | 控件初始值 |

### 10. 日期控件专用属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `initVal` | 初始值 | 日期初始值 |
| `isView` | 是否只读 | 日期控件只读模式 |

### 11. 图像控件专用属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `src` | `src` | 图片源地址 |
| `width` | `width` | 图片宽度 |
| `border` | `border` | 图片边框 |
| `alt` | `alt` | 图片替代文本 |
| `hspace` | `hspace` | 水平间距 |
| `vspace` | `vspace` | 垂直间距 |
| `hrefExp` | 链接表达式 | 图片链接条件 |

### 12. 按钮控件专用属性

| YDP按钮类型 | 转换结果 | 说明 |
|-------------|----------|------|
| `_type="submit"` | `type="submit"` | 表单提交按钮 |
| `_type="return"` | `type="button" onclick="window.history.back()"` | 返回按钮 |
| `_type="reset"` | `type="reset"` | 表单重置按钮 |
| `_type="excel"` | `type="button" onclick="window.navigate('excel?...')"` | Excel导出按钮 |
| `_type="pdf"` | `type="button" onclick="window.navigate('pdf?...')"` | PDF导出按钮 |
| `_type="word"` | `type="button" onclick="window.navigate('word?...')"` | Word导出按钮 |

### 13. 表格和布局属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `dynamictag` | 动态行标记 | 可增删行功能 |
| `ispage` | 分页标记 | 是否显示分页 |
| `EXT_IsEnable` | 扩展行启用 | 动态扩展行功能 |
| `EXT_NAME` | 扩展名称 | 动态行名称 |
| `EXT_strSql` | 扩展SQL | 动态行数据查询 |

### 14. 隐式提交属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `isframe` | `onblur="iframesubmit('...')"` | 是否启用隐式提交，转换为onblur事件 |
| `iframenum` | 提交编号 | 隐式提交序号，用于生成JavaScript函数参数 |
| `iframeisret` | 返回标记 | 是否返回提交信息 |
| `iframeretmes` | 返回消息 | 提交返回消息 |
| `triggername` | 触发器名称 | 隐式提交触发器名称 |
| `targetname` | 目标名称 | 目标控件名称 |
| `targetvalue` | 目标值 | 目标控件值 |
| `targetshow` | 目标显示 | 目标显示方式 |
| `targettype` | 目标类型 | 目标控件类型 |

### 15. 统计图表属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `graphtype` | 图表类型 | 统计图表类型 |
| `xtitle` | X轴标题 | X轴标题文本 |
| `ytitle` | Y轴标题 | Y轴标题文本 |
| `timetype` | 时间类型 | 时间精确度 |
| `ispercent` | 百分比 | 是否显示百分比 |
| `isaverage` | 平均值 | 是否显示平均值 |
| `graphwidth` | 图表宽度 | 图表显示宽度 |
| `grahpheight` | 图表高度 | 图表显示高度 |

### 16. 树形控件属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `sql` | 树形数据SQL | 树形结构查询 |
| `prValue` | 根节点值 | 树形根节点 |
| `desc` | 节点描述 | 树形节点描述 |
| `isint` | 整数标记 | 是否整数类型 |
| `isWholePath` | 完整路径 | 是否显示完整路径 |

### 17. 多级字典属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `viewname` | 视图名称 | 字典视图名 |
| `levelnum` | 级别数量 | 字典级别数 |
| `sorttype` | 排序类型 | H=水平,V=垂直 |
| `beginvalue` | 起始值 | 字典起始值 |
| `typeID` | 类型ID | 字典类型标识 |
| `level` | 级别 | 字典级别 |

### 18. 打印相关属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `isrotate` | 旋转打印 | 是否横向打印 |
| `pagesize` | 页面大小 | A3,A4,A5等 |

### 19. 扩展行相关属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `isUseExtLine` | 扩展行使用 | 是否使用动态扩展行 |
| `EXT_LINETAG` | 扩展行标记 | 扩展行标识 |
| `extname` | 扩展名称 | 动态行名称 |
| `EXT_fdname` | 扩展字段名 | 扩展字段名称 |
| `EXT_fddesc` | 扩展字段描述 | 扩展字段描述 |
| `EXT_fdIsEdit` | 扩展字段可编辑 | 是否可编辑 |
| `EXT_fdIsHidden` | 扩展字段隐藏 | 是否隐藏 |
| `EXT_fdListDic` | 扩展字段字典 | 动态扩展行字典 |

### 20. 数据源相关属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `key` | 数据键名 | 数据存储键名 |
| `maxrows` | 最大行数 | 查询最大行数限制 |
| `scope_all` | 全局范围 | 数据范围标记 |
| `isext` | 是否扩展 | 是否扩展查询 |
| `extvalue` | 扩展值 | 扩展查询值 |

### 21. 表格布局属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `isdisplay` | 是否显示 | 控制元素显示 |
| `row` | 行数 | 表格行数 |
| `col` | 列数 | 表格列数 |
| `span` | 跨度 | 单元格跨度 |
| `icon` | 图标 | 显示图标 |

### 22. 超级下拉框属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `isSuperCombo` | 超级下拉框 | 增强型下拉框标记 |
| `combobox` | 组合框 | 组合框类型 |

### 23. 单值字典属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `typeid` | 类型ID | 字典类型标识 |
| `typedesc` | 类型描述 | 字典类型描述 |
| `itemid` | 项目ID | 字典项目标识 |
| `itemdesc` | 项目描述 | 字典项目描述 |

### 24. 事件相关属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `eventType` | 事件类型 | onClick, onBlur等 |
| `eventScript` | 事件脚本 | JavaScript代码 |
| `triggername` | 触发器名称 | 隐式提交触发器 |
| `targetname` | 目标名称 | 目标控件名称 |
| `targetvalue` | 目标值 | 目标控件值 |
| `targetshow` | 目标显示 | 目标显示方式 |
| `targettype` | 目标类型 | 目标控件类型 |

### 25. 其他系统属性

| YDP属性 | HTML属性 | 说明 |
|---------|----------|------|
| `_vdaType` | VDA类型 | 页面类型标识 |
| `_delNum` | 删除编号 | 删除行编号 |
| `id` | ID标识 | 元素唯一标识 |
| `script` | 脚本 | JavaScript脚本 |

### 26. 完整属性分类总结

#### 基础属性 (所有控件通用)
- `name`, `value`, `class`, `align`, `height`, `width`
- `title`, `action`, `target`, `href`, `exp`

#### 验证属性 (表单控件)
- `_required`, `_type`, `_desc`, `_maxlength`, `_minlength`
- `_maxvalue`, `_minvalue`, `_pwd2`, `_samevaluefield`

#### 控制属性 (显示和行为)
- `readOnlyExp`, `hiddenExp`, `sign`, `readonly`
- `isframe`, `iframenum`, `dynamictag`, `ispage`

#### 样式属性 (外观)
- `text-align`, `background-color`, `color`, `font-*`
- `border-*-color`, `border-*-style`, `border-*-width`

#### 数据属性 (数据绑定)
- `initVal`, `sql`, `datasourcename`, `key`, `maxrows`
- `dictype`, `viewname`, `levelnum`, `sorttype`

#### 特殊功能属性 (高级功能)
- 图表: `graphtype`, `xtitle`, `ytitle`, `ispercent`
- 树形: `prValue`, `desc`, `isint`, `isWholePath`
- 多级字典: `typeID`, `level`, `beginvalue`
- 扩展行: `EXT_IsEnable`, `EXT_NAME`, `EXT_strSql`

## 特殊转换逻辑

### 7. 表达式处理
- **格式**: 以`=`开头的表达式会被`ParseExpression.mathExp()`解析
- **示例**: `value="=pool.get('username')"` → 从数据池获取值
- **用途**: 动态值计算、条件判断

### 8. 循环数据处理
- **标识**: 包含`#`符号的表达式表示循环数据
- **处理**: `StringUtil.replaceString()`替换行号
- **示例**: `value="=data[#].name"` → 循环显示数据

### 9. 超链接处理
- **YDP**: `href`属性 + `exp`条件表达式
- **HTML**: `<a href="...">` 
- **特殊**: 支持`.ydp`文件链接自动转换

### 10. 事件处理
- **YDP**: `<event eventType="onClick" eventScript="..."/>`
- **HTML**: `onclick="..."` 等事件属性
- **支持**: 各种JavaScript事件

## 数据流转换

### 11. 数据源处理
```xml
<data_source>
  <sql key="userList" maxrows="100">
    SELECT * FROM users WHERE status = 1
  </sql>
</data_source>
```
转换为数据库查询，结果存储在DataPool中，key为"userList"

### 12. 参数处理
```xml
<paras>
  <para>username</para>
  <para>userid</para>
</paras>
```
从request参数中提取值，存储到DataPool中

## 输出结果
最终生成完整的HTML页面，包括：
- HTML头部（通过IncludesJSP生成）
- JavaScript脚本（表单提交、验证等）
- 表单和控件
- 样式定义
- HTML尾部

## 转换示例

### 13. 完整转换示例

#### YDP输入示例:
```xml
<page>
  <paras>
    <para>username</para>
  </paras>

  <data_source>
    <sql key="userList" maxrows="50">
      SELECT id, name, email FROM users WHERE name LIKE '%=username%'
    </sql>
  </data_source>

  <form name="userForm" title="用户管理" action="/saveUser">
    <table>
      <th>
        <col>100</col>
        <col>200</col>
        <col>150</col>
      </th>

      <tr height="30" class="header">
        <td align="center">
          <value>用户ID</value>
        </td>
        <td align="center">
          <value>用户名</value>
        </td>
        <td align="center">
          <value>邮箱</value>
        </td>
      </tr>

      <tr height="25">
        <td>
          <value>=userList[#].id</value>
        </td>
        <td>
          <input type="text" name="userName" value="=userList[#].name"
                 _required="true" maxlength="50">
            <style>
              <font-size>12px</font-size>
              <font-family>宋体</font-family>
            </style>
          </input>
        </td>
        <td>
          <input type="text" name="userEmail" value="=userList[#].email"
                 _type="email" size="20"/>
        </td>
      </tr>

      <tr height="35">
        <td colspan="3" align="center">
          <input type="button" name="saveBtn" value="保存"
                 _type="submit"/>
          <input type="button" name="cancelBtn" value="取消"
                 _type="return"/>
        </td>
      </tr>
    </table>
  </form>
</page>
```

#### 转换后HTML输出:
```html
<!DOCTYPE html>
<html>
<head>
  <title>华信永道(北京)科技有限公司 - 用户管理</title>
  <!-- CSS和JS引用 -->
</head>
<body onload="vdaOnload();">

<script>
var _pn='userForm';
var _poolSel={'_IS':'...'};

function turnPage(pageno) {
  userForm.action=location.href.split('&currentpageno')[0]+'&currentpageno=' + pageno;
  userForm.submit();
}

function iframesubmit(num) {
  // 隐式提交逻辑
}
</script>

<fgbz>
<form method='post' name="userForm" action="/saveUser" onsubmit="return false" target='_self'>
  <input type='hidden' name='_signText'>
  <input type='hidden' name='_CHANNEL' value='1'>
  <input type='hidden' name='_MAXROWCOUNTNUM' value='1000'>

  <table border='0' cellspacing='0' cellpadding='3' style='border-collapse:collapse'>
    <colgroup style="width:100px;"></colgroup>
    <colgroup style="width:200px;"></colgroup>
    <colgroup style="width:150px;"></colgroup>

    <tr height="30" class="header">
      <td nowrap align="center">用户ID</td>
      <td nowrap align="center">用户名</td>
      <td nowrap align="center">邮箱</td>
    </tr>

    <!-- 循环生成的数据行 -->
    <tr beginrow=1 height="25" class="InputTrji">
      <td nowrap>001</td>
      <td nowrap>
        <input name="userName_1" type="text" value="张三"
               _required="true" maxlength="50"
               style="font-size:12px;font-family:宋体;">
        <font color=red>*</font>
      </td>
      <td nowrap>
        <input name="userEmail_1" type="text" value="<EMAIL>"
               _type="email" size="20">
      </td>
    </tr>

    <tr beginrow=2 height="25" class="InputTrou">
      <td nowrap>002</td>
      <td nowrap>
        <input name="userName_2" type="text" value="李四"
               _required="true" maxlength="50"
               style="font-size:12px;font-family:宋体;">
        <font color=red>*</font>
      </td>
      <td nowrap>
        <input name="userEmail_2" type="text" value="<EMAIL>"
               _type="email" size="20">
      </td>
    </tr>

    <tr height="35">
      <td nowrap colspan="3" align="center">
        <input name="saveBtn" type="button" value="保存" onclick="submitForm();">
        <input name="cancelBtn" type="button" value="取消" onclick="history.back();">
      </td>
    </tr>
  </table>
</form>
</fgbz>

</body>
</html>
```

## 关键转换规则总结

### 14. 核心转换规则
1. **标签映射**: YDP标签 → HTML标签
2. **属性转换**: YDP属性 → HTML属性
3. **表达式解析**: `=expression` → 动态值
4. **循环处理**: `#` → 行号替换
5. **样式转换**: YDP样式 → CSS样式
6. **事件绑定**: YDP事件 → JavaScript事件
7. **数据绑定**: DataPool → HTML值填充
8. **验证规则**: YDP验证 → JavaScript验证

### 15. 特殊处理逻辑
- **必填字段**: `_required="true"` → 添加红色星号标记
- **只读控件**: `readOnlyExp` → 灰色背景样式
- **货币格式**: `_type="money"` → 自动格式化显示
- **数字验证**: `_type="int"` → 键盘事件过滤
- **分页显示**: `ispage="true"` → 生成分页控件
- **动态行**: `dynamictag="true"` → 可增删行功能

## 详细控件转换示例

### 16. 按钮控件转换

#### YDP按钮定义:
```xml
<input type="button" name="saveBtn" value="保存" _type="submit">
  <event eventType="onClick" eventScript="validateForm();"/>
</input>

<input type="button" name="backBtn" value="返回" _type="return"/>

<input type="button" name="excelBtn" value="导出Excel" _type="excel"/>
```

#### 转换后HTML:
```html
<input name="saveBtn" type="submit" onclick="validateForm();" value="保存">

<input name="backBtn" type="button" onclick="window.history.back()" value="返回">

<input name="excelBtn" type="button"
       onclick="window.navigate('excel?filename=userList.ydp&currentpageno=1&...')"
       value="导出Excel">
```

### 17. 下拉框控件转换

#### YDP下拉框定义:
```xml
<input type="select" name="userType" value="=currentUserType" _required="true">
  <!-- 静态选项 -->
  <option value="1" label="管理员"/>
  <option value="2" label="普通用户"/>

  <!-- 动态选项 -->
  <option value="=typeList[#].id" label="=typeList[#].name"/>
</input>
```

#### 转换后HTML:
```html
<select name="userType" _required="true">
  <option value="">==请选择==</option>
  <option value="1">管理员</option>
  <option value="2" selected>普通用户</option>
  <option value="3">访客</option>
</select>
<font color=red>*</font>
```

### 18. 文本框控件转换

#### YDP文本框定义:
```xml
<input type="text" name="userName" value="=user.name"
       _required="true" _type="string" maxlength="50" size="20"
       readOnlyExp="=user.status == 'locked'">
  <style>
    <font-size>14px</font-size>
    <font-family>Arial</font-family>
    <font-weight>true</font-weight>
  </style>
  <event eventType="onBlur" eventScript="checkUserName(this.value);"/>
</input>
```

#### 转换后HTML:
```html
<input name="userName" type="text" value="张三"
       _required="true" _type="string" maxlength="50" size="20"
       style="font-size:14px;font-family:Arial;font-weight:bold;"
       onblur="checkUserName(this.value);">
<font color=red>*</font>
```

### 19. 复选框控件转换

#### YDP复选框定义:
```xml
<input type="checkbox" name="permissions" value="=userPermissions">
  <option value="read" label="读取权限"/>
  <option value="write" label="写入权限"/>
  <option value="delete" label="删除权限"/>
</input>
```

#### 转换后HTML:
```html
<input type="checkbox" name="permissions" value="read" checked> 读取权限<br>
<input type="checkbox" name="permissions" value="write"> 写入权限<br>
<input type="checkbox" name="permissions" value="delete" checked> 删除权限<br>
```

### 20. 单选框控件转换

#### YDP单选框定义:
```xml
<input type="radio" name="gender" value="=user.gender">
  <option value="M" label="男"/>
  <option value="F" label="女"/>
</input>
```

#### 转换后HTML:
```html
<input type="radio" name="gender" value="M" checked> 男
<input type="radio" name="gender" value="F"> 女
```

## 数据绑定和表达式处理

### 21. 表达式类型详解

| 表达式类型 | 格式 | 示例 | 说明 |
|------------|------|------|------|
| 静态值 | 直接文本 | `张三` | 固定文本值 |
| 数据池取值 | `=变量名` | `=userName` | 从DataPool获取值 |
| 数组循环 | `=数组[#].字段` | `=userList[#].name` | 循环数组数据 |
| 条件表达式 | `=条件?值1:值2` | `=status=='1'?'启用':'禁用'` | 条件判断 |
| 函数调用 | `=函数名(参数)` | `=formatDate(createTime)` | 调用函数 |
| 复合表达式 | `=表达式组合` | `=user.name + '(' + user.code + ')'` | 多个表达式组合 |

### 22. 样式转换详解

#### YDP样式定义:
```xml
<style>
  <text-align>center</text-align>
  <background-color>255,255,0</background-color>
  <font-size>12px</font-size>
  <font-weight>true</font-weight>
  <border-bottom-style>solid</border-bottom-style>
  <border-bottom-width>1px</border-bottom-width>
  <border-bottom-color>0,0,0</border-bottom-color>
</style>
```

#### 转换后CSS:
```html
style="text-align:center;background-color:#FFFF00;font-size:12px;
       font-weight:bold;border-bottom-style:solid;border-bottom-width:1px;
       border-bottom-color:#000000;"
```

**转换说明**:
- `background-color="255,255,0"` → `background-color:#FFFF00` (RGB转十六进制)
- `font-weight="true"` → `font-weight:bold` (布尔值转换)
- `border-bottom-color="0,0,0"` → `border-bottom-color:#000000` (RGB转十六进制)

## 转换流程总结

### 23. 完整转换流程

```
YDP文件 → XMLReader.parseFromXML()
       → HtmlModel.expandElementNode()
       → 各种Proxy类处理
       → 生成HTML字符串
       → 输出到浏览器
```

#### 详细步骤:
1. **XML解析**: 使用JDOM解析YDP文件为DOM树
2. **节点遍历**: 递归遍历所有XML节点
3. **标签识别**: 根据标签名调用相应处理方法
4. **属性转换**: 将YDP属性转换为HTML属性
5. **表达式计算**: 解析和计算动态表达式
6. **样式处理**: 转换YDP样式为CSS样式
7. **事件绑定**: 生成JavaScript事件代码
8. **HTML生成**: 组装最终的HTML代码

### 24. 核心转换规则汇总

| 转换类型 | YDP格式 | HTML格式 | 处理类 |
|----------|---------|----------|--------|
| 页面结构 | `<page>` | HTML页面框架 | HtmlModel.expandElementNode |
| 表单 | `<form>` | `<form>` + JS脚本 | HtmlModel.execForm |
| 表格容器 | `<table>` | `<table>` | **未直接处理，execForm假设处理** |
| 表格行 | `<tr>` | `<tr>` | HtmlModel.genTRString |
| 表格单元格 | `<td>` | `<td>` | HtmlModel.getTDHeadValue + showTdCellText |
| 列宽定义 | `<th>` | `<colgroup>` | HtmlModel.genTHString |
| 文本框 | `<input type="text">` | `<input type="text">` | TextProxy |
| 下拉框 | `<input type="select">` | `<select><option>` | SelectProxy |
| 按钮 | `<input type="button">` | `<input type="button">` | ButtonProxy |
| 复选框 | `<input type="checkbox">` | `<input type="checkbox">` | CheckBoxProxy |
| 单选框 | `<input type="radio">` | `<input type="radio">` | RadioProxy |
| 文本域 | `<input type="textarea">` | `<textarea>` | TextAreaProxy |
| 数据源 | `<data_source>` | 数据库查询 | HtmlModel.execDataSource |
| 参数 | `<paras>` | 参数提取 | HtmlModel.setParaToPool |

### 25. 特殊功能转换

| 功能 | YDP实现 | HTML实现 | 说明 |
|------|---------|----------|------|
| 数据循环 | `=data[#].field` | 动态生成多行 | 自动循环数据 |
| 条件显示 | `hiddenExp="expression"` | 控制元素显示/隐藏 | 条件渲染 |
| 只读控制 | `readOnlyExp="expression"` | `readonly`属性 | 动态只读 |
| 必填验证 | `_required="true"` | 红色星号标记 | 客户端验证 |
| 数据格式化 | `showstyle`属性 | 格式化显示 | 数据美化 |
| 分页显示 | `ispage="true"` | 分页控件 | 数据分页 |
| 隐式提交 | `isframe="true"` | Ajax提交 | 异步操作 |
| 动态行 | `dynamictag="true"` | 可增删行 | 动态表格 |

## 关键类说明
- `HtmlModel`: 主转换类，继承XMLReader，负责整体转换流程
- `XMLReader`: 基础XML解析类，定义解析框架和节点遍历逻辑
- `HtmlConstants`: 常量定义类，包含所有标签名和属性名常量
- `*Proxy`: 各种控件的代理转换类，负责具体HTML生成
  - `TextProxy`: 文本框转换
  - `SelectProxy`: 下拉框转换
  - `ButtonProxy`: 按钮转换
  - `CheckBoxProxy`: 复选框转换
  - `RadioProxy`: 单选框转换
  - 等等...
- `DataPool`: 数据存储池，保存页面数据和参数
- `ParseExpression`: 表达式解析器，处理动态表达式计算
- `IncludesJSP`: 页面头尾生成器，包含CSS和JS引用
- `Tools`: 工具类，提供颜色转换等辅助功能

## 转换限制和注意事项

### 26. 未处理的标签

#### td-child标签
- **定义**: 在HtmlConstants中定义为`TD_CHILD = "td-child"`
- **状态**: **未参与HTML转换过程**
- **用途**: 用于表示跨列单元格内部的逻辑子单元格结构
- **说明**: 虽然在YDP文件中可以定义，但在HtmlModel的转换逻辑中没有对应的处理代码

#### EXT_LINETAG标签
- **定义**: 在HtmlConstants中定义为`EXT_LINETAG = "EXT_LINETAG"`
- **状态**: **仅在动态扩展行功能中使用**
- **处理**: 在genTRString方法中，当`EXT_IsEnable="true"`时会处理此标签
- **用途**: 配置动态扩展行的SQL查询和字段定义

### 27. 转换覆盖范围

#### 完全支持的标签
- `<page>`, `<form>`, `<table>`, `<tr>`, `<td>`, `<th>`
- `<paras>`, `<data_source>`, `<iframes>`
- `<input>` 及其所有type类型
- `<style>`, `<value>`, `<option>`, `<event>`

#### 部分支持的标签
- `<EXT_LINETAG>`: 仅在动态扩展行功能中使用

#### 不支持的标签
- `<table>`: 虽然定义了`HtmlConstants.TABLE`常量，但`expandElementNode`和`execForm`都没有检查此标签名
- `<td-child>`: 定义了常量但未实现转换逻辑

#### 特殊处理机制
- **表格处理**: `execForm`方法假设form的所有子元素都是table，直接为每个子元素生成`<table>`标签，而不检查元素名称是否为`HtmlConstants.TABLE`

## 总结

YDP转换系统是一个完整的XML到HTML的转换框架，通过以下特点实现了灵活的页面生成：

1. **标签映射**: 将自定义YDP标签映射为标准HTML标签
2. **属性转换**: 智能转换YDP属性为HTML属性和CSS样式
3. **表达式支持**: 支持动态表达式计算和数据绑定
4. **控件代理**: 使用代理模式处理不同类型的控件转换
5. **数据集成**: 集成数据库查询和参数处理
6. **样式转换**: 自动转换YDP样式为CSS样式
7. **事件绑定**: 支持JavaScript事件绑定
8. **扩展性**: 通过代理类可以轻松扩展新的控件类型

这个转换系统为开发者提供了一种声明式的页面开发方式，通过XML配置即可生成复杂的HTML页面。

## 文档修正记录

本文档基于代码分析进行了以下重要修正：

### 修正的错误
1. **表格转换映射错误**: 经过详细代码分析发现：
   - `<table>` 标签**没有被直接处理**，`expandElementNode`方法不检查`HtmlConstants.TABLE`
   - `execForm` 方法假设form的所有子元素都是table，直接生成`<table>`标签
   - `<tr>` 标签由 `HtmlModel.genTRString` 方法处理
   - `<td>` 标签由 `HtmlModel.getTDHeadValue` 和 `showTdCellText` 方法处理

2. **按钮类型转换**: 明确了不同按钮类型的具体HTML转换结果

3. **隐式提交属性**: 补充了完整的隐式提交相关属性说明

4. **样式转换**: 详细说明了颜色值转换和布尔值转换的具体规则

### 补充的内容
1. **未处理标签**: 明确说明了`td-child`和`EXT_LINETAG`标签的处理状态
2. **转换覆盖范围**: 列出了完全支持、部分支持和不支持的标签
3. **处理方法**: 为每个标签映射添加了具体的处理方法信息

### 验证方法
所有修正都基于对源代码的详细分析，确保了文档的准确性和完整性。
